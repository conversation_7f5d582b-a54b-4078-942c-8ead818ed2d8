{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PathGizmo.ts"], "names": ["Color", "Vec3", "GizmoDrawer", "RegisterGizmoDrawer", "Gizmo<PERSON><PERSON>s", "PathBaker", "PathGizmo", "componentType", "drawerName", "pathColor", "CYAN", "controlPointColor", "YELLOW", "onPathPointColor", "GREEN", "offPathPointColor", "RED", "lineWidth", "pointRadius", "controlLineWidth", "drawGizmos", "component", "graphics", "node", "points", "length", "worldPositions", "getWorldPositions", "drawSinglePoint", "isOnPath", "segments", "parsePathSegments", "drawPathSegments", "drawControlElements", "pointBakers", "map", "pointBaker", "worldPos", "worldPosition", "gizmoPos", "worldToGizmoSpace", "parent", "x", "y", "positions", "i", "currentPoint", "nextOnPathIndex", "findNextOnPathPoint", "offPathCount", "push", "type", "handleComplexSegment", "startIndex", "endIndex", "controlPoints", "start", "end", "segment", "drawLineSegment", "drawQuadraticSegment", "drawCubicSegment", "position", "color", "drawCircle", "drawLine", "control", "drawQuadraticBezier", "control1", "control2", "drawCubicBezier", "drawControlHandles"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA+BA,MAAAA,K,OAAAA,K;AAAaC,MAAAA,I,OAAAA,I;;AACnCC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,mB,iBAAAA,mB;;AACbC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;AAIT;AACA;AACA;2BAOaC,S;;oEADb,MACaA,SADb;AAAA;AAAA,sCACsD;AAAA;AAAA;AAAA,eAElCC,aAFkC;AAAA;AAAA;AAAA,eAGlCC,UAHkC,GAGrB,WAHqB;AAKlD;AALkD,eAMjCC,SANiC,GAMrBT,KAAK,CAACU,IANe;AAAA,eAOjCC,iBAPiC,GAObX,KAAK,CAACY,MAPO;AAAA,eAQjCC,gBARiC,GAQdb,KAAK,CAACc,KARQ;AAAA,eASjCC,iBATiC,GASbf,KAAK,CAACgB,GATO;AAAA,eAUjCC,SAViC,GAUrB,CAVqB;AAAA,eAWjCC,WAXiC,GAWnB,CAXmB;AAAA,eAYjCC,gBAZiC,GAYd,CAZc;AAAA;;AAc3CC,QAAAA,UAAU,CAACC,SAAD,EAAuBC,QAAvB,EAA2CC,IAA3C,EAA6D;AAC1E,cAAMC,MAAM,GAAGH,SAAS,CAACG,MAAzB;;AAEA,cAAI,CAACA,MAAD,IAAWA,MAAM,CAACC,MAAP,KAAkB,CAAjC,EAAoC;AAChC,mBADgC,CACxB;AACX,WALyE,CAO1E;;;AACA,cAAMC,cAAc,GAAG,KAAKC,iBAAL,CAAuBH,MAAvB,CAAvB;;AAEA,cAAIE,cAAc,CAACD,MAAf,GAAwB,CAA5B,EAA+B;AAC3B;AACA,iBAAKG,eAAL,CAAqBN,QAArB,EAA+BI,cAAc,CAAC,CAAD,CAA7C,EAAkDF,MAAM,CAAC,CAAD,CAAN,CAAUK,QAA5D;AACA;AACH,WAdyE,CAgB1E;;;AACA,cAAMC,QAAQ,GAAG,KAAKC,iBAAL,CAAuBL,cAAvB,EAAuCF,MAAvC,CAAjB,CAjB0E,CAmB1E;;AACA,eAAKQ,gBAAL,CAAsBV,QAAtB,EAAgCQ,QAAhC,EApB0E,CAsB1E;;AACA,eAAKG,mBAAL,CAAyBX,QAAzB,EAAmCI,cAAnC,EAAmDF,MAAnD,EAA2DM,QAA3D;AACH;AAED;AACJ;AACA;;;AACYH,QAAAA,iBAAiB,CAACO,WAAD,EAAoC;AACzD,iBAAOA,WAAW,CAACC,GAAZ,CAAgBC,UAAU,IAAI;AACjC,gBAAMC,QAAQ,GAAGD,UAAU,CAACb,IAAX,CAAgBe,aAAjC;AACA,gBAAMC,QAAQ,GAAG,KAAKC,iBAAL,CAAuBH,QAAvB,EAAiCH,WAAW,CAAC,CAAD,CAAX,CAAeX,IAAf,CAAoBkB,MAArD,CAAjB;AACA,mBAAO,IAAIxC,IAAJ,CAASsC,QAAQ,CAACG,CAAlB,EAAqBH,QAAQ,CAACI,CAA9B,EAAiC,CAAjC,CAAP;AACH,WAJM,CAAP;AAKH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACYZ,QAAAA,iBAAiB,CAACa,SAAD,EAAoBV,WAApB,EAA8D;AACnF,cAAMJ,QAAuB,GAAG,EAAhC;AACA,cAAIe,CAAC,GAAG,CAAR;;AAEA,iBAAOA,CAAC,GAAGX,WAAW,CAACT,MAAvB,EAA+B;AAC3B,gBAAMqB,YAAY,GAAGZ,WAAW,CAACW,CAAD,CAAhC;;AAEA,gBAAI,CAACC,YAAY,CAACjB,QAAlB,EAA4B;AACxB;AACAgB,cAAAA,CAAC;AACD;AACH,aAP0B,CAS3B;;;AACA,gBAAIE,eAAe,GAAG,KAAKC,mBAAL,CAAyBd,WAAzB,EAAsCW,CAAC,GAAG,CAA1C,CAAtB;;AAEA,gBAAIE,eAAe,KAAK,CAAC,CAAzB,EAA4B;AACxB;AACA;AACH,aAf0B,CAiB3B;;;AACA,gBAAME,YAAY,GAAGF,eAAe,GAAGF,CAAlB,GAAsB,CAA3C;;AAEA,gBAAII,YAAY,KAAK,CAArB,EAAwB;AACpB;AACAnB,cAAAA,QAAQ,CAACoB,IAAT,CAAc;AACVC,gBAAAA,IAAI,EAAE,MADI;AAEV3B,gBAAAA,MAAM,EAAE,CAACoB,SAAS,CAACC,CAAD,CAAV,EAAeD,SAAS,CAACG,eAAD,CAAxB;AAFE,eAAd;AAIH,aAND,MAMO,IAAIE,YAAY,KAAK,CAArB,EAAwB;AAC3B;AACAnB,cAAAA,QAAQ,CAACoB,IAAT,CAAc;AACVC,gBAAAA,IAAI,EAAE,WADI;AAEV3B,gBAAAA,MAAM,EAAE,CAACoB,SAAS,CAACC,CAAD,CAAV,EAAeD,SAAS,CAACC,CAAC,GAAG,CAAL,CAAxB,EAAiCD,SAAS,CAACG,eAAD,CAA1C;AAFE,eAAd;AAIH,aANM,MAMA,IAAIE,YAAY,KAAK,CAArB,EAAwB;AAC3B;AACAnB,cAAAA,QAAQ,CAACoB,IAAT,CAAc;AACVC,gBAAAA,IAAI,EAAE,OADI;AAEV3B,gBAAAA,MAAM,EAAE,CAACoB,SAAS,CAACC,CAAD,CAAV,EAAeD,SAAS,CAACC,CAAC,GAAG,CAAL,CAAxB,EAAiCD,SAAS,CAACC,CAAC,GAAG,CAAL,CAA1C,EAAmDD,SAAS,CAACG,eAAD,CAA5D;AAFE,eAAd;AAIH,aANM,MAMA;AACH;AACA;AACA,mBAAKK,oBAAL,CAA0BtB,QAA1B,EAAoCc,SAApC,EAA+CC,CAA/C,EAAkDE,eAAlD;AACH;;AAEDF,YAAAA,CAAC,GAAGE,eAAJ;AACH;;AAED,iBAAOjB,QAAP;AACH;AAED;AACJ;AACA;;;AACYkB,QAAAA,mBAAmB,CAACd,WAAD,EAA4BmB,UAA5B,EAAwD;AAC/E,eAAK,IAAIR,CAAC,GAAGQ,UAAb,EAAyBR,CAAC,GAAGX,WAAW,CAACT,MAAzC,EAAiDoB,CAAC,EAAlD,EAAsD;AAClD,gBAAIX,WAAW,CAACW,CAAD,CAAX,CAAehB,QAAnB,EAA6B;AACzB,qBAAOgB,CAAP;AACH;AACJ;;AACD,iBAAO,CAAC,CAAR;AACH;AAED;AACJ;AACA;AACA;;;AACYO,QAAAA,oBAAoB,CAACtB,QAAD,EAA0Bc,SAA1B,EAA6CS,UAA7C,EAAiEC,QAAjE,EAAyF;AACjH;AACA;AAEA,cAAMC,aAAa,GAAG,EAAtB;;AACA,eAAK,IAAIV,CAAC,GAAGQ,UAAU,GAAG,CAA1B,EAA6BR,CAAC,GAAGS,QAAjC,EAA2CT,CAAC,EAA5C,EAAgD;AAC5CU,YAAAA,aAAa,CAACL,IAAd,CAAmBN,SAAS,CAACC,CAAD,CAA5B;AACH;;AAED,cAAIU,aAAa,CAAC9B,MAAd,IAAwB,CAA5B,EAA+B;AAC3B;AACA,iBAAK,IAAIoB,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGU,aAAa,CAAC9B,MAAd,GAAuB,CAA3C,EAA8CoB,EAAC,IAAI,CAAnD,EAAsD;AAClD,kBAAMW,KAAK,GAAGX,EAAC,KAAK,CAAN,GAAUD,SAAS,CAACS,UAAD,CAAnB,GAAkCE,aAAa,CAACV,EAAC,GAAG,CAAL,CAA7D;AACA,kBAAMY,GAAG,GAAGZ,EAAC,GAAG,CAAJ,IAASU,aAAa,CAAC9B,MAAvB,GAAgCmB,SAAS,CAACU,QAAD,CAAzC,GAAsDC,aAAa,CAACV,EAAC,GAAG,CAAL,CAA/E;AAEAf,cAAAA,QAAQ,CAACoB,IAAT,CAAc;AACVC,gBAAAA,IAAI,EAAE,OADI;AAEV3B,gBAAAA,MAAM,EAAE,CAACgC,KAAD,EAAQD,aAAa,CAACV,EAAD,CAArB,EAA0BU,aAAa,CAACV,EAAC,GAAG,CAAL,CAAvC,EAAgDY,GAAhD;AAFE,eAAd;AAIH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYzB,QAAAA,gBAAgB,CAACV,QAAD,EAAqBQ,QAArB,EAAoD;AACxE,eAAK,IAAM4B,OAAX,IAAsB5B,QAAtB,EAAgC;AAC5B,oBAAQ4B,OAAO,CAACP,IAAhB;AACI,mBAAK,MAAL;AACI,qBAAKQ,eAAL,CAAqBrC,QAArB,EAA+BoC,OAAO,CAAClC,MAAvC;AACA;;AACJ,mBAAK,WAAL;AACI,qBAAKoC,oBAAL,CAA0BtC,QAA1B,EAAoCoC,OAAO,CAAClC,MAA5C;AACA;;AACJ,mBAAK,OAAL;AACI,qBAAKqC,gBAAL,CAAsBvC,QAAtB,EAAgCoC,OAAO,CAAClC,MAAxC;AACA;AATR;AAWH;AACJ;AAED;AACJ;AACA;;;AACYI,QAAAA,eAAe,CAACN,QAAD,EAAqBwC,QAArB,EAAqCjC,QAArC,EAA8D;AACjF,cAAMkC,KAAK,GAAGlC,QAAQ,GAAG,KAAKhB,gBAAR,GAA2B,KAAKE,iBAAtD;AACA;AAAA;AAAA,wCAAWiD,UAAX,CAAsB1C,QAAtB,EAAgCwC,QAAQ,CAACpB,CAAzC,EAA4CoB,QAAQ,CAACnB,CAArD,EAAwD,KAAKzB,WAA7D,EAA0E6C,KAA1E,EAAiFlC,QAAjF;AACH;AAED;AACJ;AACA;;;AACY8B,QAAAA,eAAe,CAACrC,QAAD,EAAqBE,MAArB,EAA2C;AAC9D,cAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;AAEvB,cAAM+B,KAAK,GAAGhC,MAAM,CAAC,CAAD,CAApB;AACA,cAAMiC,GAAG,GAAGjC,MAAM,CAAC,CAAD,CAAlB;AACA;AAAA;AAAA,wCAAWyC,QAAX,CAAoB3C,QAApB,EAA8BkC,KAAK,CAACd,CAApC,EAAuCc,KAAK,CAACb,CAA7C,EAAgDc,GAAG,CAACf,CAApD,EAAuDe,GAAG,CAACd,CAA3D,EAA8D,KAAKlC,SAAnE,EAA8E,KAAKQ,SAAnF;AACH;AAED;AACJ;AACA;;;AACY2C,QAAAA,oBAAoB,CAACtC,QAAD,EAAqBE,MAArB,EAA2C;AACnE,cAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;AAEvB,cAAM+B,KAAK,GAAGhC,MAAM,CAAC,CAAD,CAApB;AACA,cAAM0C,OAAO,GAAG1C,MAAM,CAAC,CAAD,CAAtB;AACA,cAAMiC,GAAG,GAAGjC,MAAM,CAAC,CAAD,CAAlB;AAEA;AAAA;AAAA,wCAAW2C,mBAAX,CACI7C,QADJ,EAEIkC,KAAK,CAACd,CAFV,EAEac,KAAK,CAACb,CAFnB,EAGIuB,OAAO,CAACxB,CAHZ,EAGewB,OAAO,CAACvB,CAHvB,EAIIc,GAAG,CAACf,CAJR,EAIWe,GAAG,CAACd,CAJf,EAKI,KAAKlC,SALT,EAMI,EANJ,EAOI,KAAKQ,SAPT;AASH;AAED;AACJ;AACA;;;AACY4C,QAAAA,gBAAgB,CAACvC,QAAD,EAAqBE,MAArB,EAA2C;AAC/D,cAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;AAEvB,cAAM+B,KAAK,GAAGhC,MAAM,CAAC,CAAD,CAApB;AACA,cAAM4C,QAAQ,GAAG5C,MAAM,CAAC,CAAD,CAAvB;AACA,cAAM6C,QAAQ,GAAG7C,MAAM,CAAC,CAAD,CAAvB;AACA,cAAMiC,GAAG,GAAGjC,MAAM,CAAC,CAAD,CAAlB;AAEA;AAAA;AAAA,wCAAW8C,eAAX,CACIhD,QADJ,EAEIkC,KAAK,CAACd,CAFV,EAEac,KAAK,CAACb,CAFnB,EAGIyB,QAAQ,CAAC1B,CAHb,EAGgB0B,QAAQ,CAACzB,CAHzB,EAII0B,QAAQ,CAAC3B,CAJb,EAIgB2B,QAAQ,CAAC1B,CAJzB,EAKIc,GAAG,CAACf,CALR,EAKWe,GAAG,CAACd,CALf,EAMI,KAAKlC,SANT,EAOI,EAPJ,EAQI,KAAKQ,SART;AAUH;AAED;AACJ;AACA;;;AACYgB,QAAAA,mBAAmB,CAACX,QAAD,EAAqBsB,SAArB,EAAwCV,WAAxC,EAAmEJ,QAAnE,EAAkG;AACzH;AACA,eAAK,IAAIe,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,SAAS,CAACnB,MAA9B,EAAsCoB,CAAC,EAAvC,EAA2C;AACvC,gBAAMiB,QAAQ,GAAGlB,SAAS,CAACC,CAAD,CAA1B;AACA,gBAAMhB,QAAQ,GAAGK,WAAW,CAACW,CAAD,CAAX,CAAehB,QAAhC;AACA,gBAAMkC,KAAK,GAAGlC,QAAQ,GAAG,KAAKhB,gBAAR,GAA2B,KAAKE,iBAAtD;AAEA;AAAA;AAAA,0CAAWiD,UAAX,CAAsB1C,QAAtB,EAAgCwC,QAAQ,CAACpB,CAAzC,EAA4CoB,QAAQ,CAACnB,CAArD,EAAwD,KAAKzB,WAA7D,EAA0E6C,KAA1E,EAAiFlC,QAAjF;AACH,WARwH,CAUzH;;;AACA,eAAK0C,kBAAL,CAAwBjD,QAAxB,EAAkCsB,SAAlC,EAA6CV,WAA7C,EAA0DJ,QAA1D;AACH;AAED;AACJ;AACA;;;AACYyC,QAAAA,kBAAkB,CAACjD,QAAD,EAAqBsB,SAArB,EAAwCV,WAAxC,EAAmEJ,QAAnE,EAAkG;AACxH,eAAK,IAAM4B,OAAX,IAAsB5B,QAAtB,EAAgC;AAC5B,gBAAI4B,OAAO,CAACP,IAAR,KAAiB,WAAjB,IAAgCO,OAAO,CAAClC,MAAR,CAAeC,MAAf,IAAyB,CAA7D,EAAgE;AAC5D;AACA,kBAAM+B,KAAK,GAAGE,OAAO,CAAClC,MAAR,CAAe,CAAf,CAAd;AACA,kBAAM0C,OAAO,GAAGR,OAAO,CAAClC,MAAR,CAAe,CAAf,CAAhB;AACA,kBAAMiC,GAAG,GAAGC,OAAO,CAAClC,MAAR,CAAe,CAAf,CAAZ;AAEA;AAAA;AAAA,4CAAWyC,QAAX,CAAoB3C,QAApB,EAA8BkC,KAAK,CAACd,CAApC,EAAuCc,KAAK,CAACb,CAA7C,EAAgDuB,OAAO,CAACxB,CAAxD,EAA2DwB,OAAO,CAACvB,CAAnE,EAAsE,KAAKhC,iBAA3E,EAA8F,KAAKQ,gBAAnG;AACA;AAAA;AAAA,4CAAW8C,QAAX,CAAoB3C,QAApB,EAA8B4C,OAAO,CAACxB,CAAtC,EAAyCwB,OAAO,CAACvB,CAAjD,EAAoDc,GAAG,CAACf,CAAxD,EAA2De,GAAG,CAACd,CAA/D,EAAkE,KAAKhC,iBAAvE,EAA0F,KAAKQ,gBAA/F;AAEH,aATD,MASO,IAAIuC,OAAO,CAACP,IAAR,KAAiB,OAAjB,IAA4BO,OAAO,CAAClC,MAAR,CAAeC,MAAf,IAAyB,CAAzD,EAA4D;AAC/D;AACA,kBAAM+B,MAAK,GAAGE,OAAO,CAAClC,MAAR,CAAe,CAAf,CAAd;AACA,kBAAM4C,QAAQ,GAAGV,OAAO,CAAClC,MAAR,CAAe,CAAf,CAAjB;AACA,kBAAM6C,QAAQ,GAAGX,OAAO,CAAClC,MAAR,CAAe,CAAf,CAAjB;AACA,kBAAMiC,IAAG,GAAGC,OAAO,CAAClC,MAAR,CAAe,CAAf,CAAZ;AAEA;AAAA;AAAA,4CAAWyC,QAAX,CAAoB3C,QAApB,EAA8BkC,MAAK,CAACd,CAApC,EAAuCc,MAAK,CAACb,CAA7C,EAAgDyB,QAAQ,CAAC1B,CAAzD,EAA4D0B,QAAQ,CAACzB,CAArE,EAAwE,KAAKhC,iBAA7E,EAAgG,KAAKQ,gBAArG;AACA;AAAA;AAAA,4CAAW8C,QAAX,CAAoB3C,QAApB,EAA8B+C,QAAQ,CAAC3B,CAAvC,EAA0C2B,QAAQ,CAAC1B,CAAnD,EAAsDc,IAAG,CAACf,CAA1D,EAA6De,IAAG,CAACd,CAAjE,EAAoE,KAAKhC,iBAAzE,EAA4F,KAAKQ,gBAAjG;AACH;AACJ;AACJ;;AAnRiD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node, Vec3 } from 'cc';\r\nimport { Giz<PERSON><PERSON>rawer, RegisterGizmoDrawer } from './GizmoDrawer';\r\nimport { GizmoUtils } from './GizmoUtils';\r\nimport { PathBaker } from '../world/level/Baker/PathBaker';\r\nimport { PathPoint } from '../world/level/Data/PathPoint';\r\nimport { PointBaker } from '../world/level/Baker/PointBaker';\r\n\r\n/**\r\n * Represents a path segment with its type and associated points\r\n */\r\ninterface PathSegment {\r\n    type: 'line' | 'quadratic' | 'cubic';\r\n    points: Vec3[];\r\n}\r\n\r\n@RegisterGizmoDrawer\r\nexport class PathGizmo extends GizmoDrawer<PathBaker> {\r\n\r\n    public readonly componentType = PathBaker;\r\n    public readonly drawerName = \"PathGizmo\";\r\n\r\n    // Visual settings\r\n    private readonly pathColor = Color.CYAN;\r\n    private readonly controlPointColor = Color.YELLOW;\r\n    private readonly onPathPointColor = Color.GREEN;\r\n    private readonly offPathPointColor = Color.RED;\r\n    private readonly lineWidth = 2;\r\n    private readonly pointRadius = 4;\r\n    private readonly controlLineWidth = 1;\r\n\r\n    public drawGizmos(component: PathBaker, graphics: Graphics, node: Node): void {\r\n        const points = component.points;\r\n\r\n        if (!points || points.length === 0) {\r\n            return; // No points to draw\r\n        }\r\n\r\n        // Convert PointBaker array to world positions\r\n        const worldPositions = this.getWorldPositions(points);\r\n\r\n        if (worldPositions.length < 2) {\r\n            // Draw single point\r\n            this.drawSinglePoint(graphics, worldPositions[0], points[0].isOnPath);\r\n            return;\r\n        }\r\n\r\n        // Parse path into segments based on isOnPath flags\r\n        const segments = this.parsePathSegments(worldPositions, points);\r\n\r\n        // Draw all segments\r\n        this.drawPathSegments(graphics, segments);\r\n\r\n        // Draw control points and handles\r\n        this.drawControlElements(graphics, worldPositions, points, segments);\r\n    }\r\n\r\n    /**\r\n     * Convert PointBaker components to world positions in gizmo space\r\n     */\r\n    private getWorldPositions(pointBakers: PointBaker[]): Vec3[] {\r\n        return pointBakers.map(pointBaker => {\r\n            const worldPos = pointBaker.node.worldPosition;\r\n            const gizmoPos = this.worldToGizmoSpace(worldPos, pointBakers[0].node.parent);\r\n            return new Vec3(gizmoPos.x, gizmoPos.y, 0);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Parse the path into segments based on isOnPath flags\r\n     * Rules:\r\n     * - 2 consecutive on-path points = line segment\r\n     * - 3 points (on, off, on) = quadratic curve\r\n     * - 4 points (on, off, off, on) = cubic curve\r\n     */\r\n    private parsePathSegments(positions: Vec3[], pointBakers: PointBaker[]): PathSegment[] {\r\n        const segments: PathSegment[] = [];\r\n        let i = 0;\r\n\r\n        while (i < pointBakers.length) {\r\n            const currentPoint = pointBakers[i];\r\n\r\n            if (!currentPoint.isOnPath) {\r\n                // Skip off-path points that aren't part of a curve\r\n                i++;\r\n                continue;\r\n            }\r\n\r\n            // Find the next on-path point\r\n            let nextOnPathIndex = this.findNextOnPathPoint(pointBakers, i + 1);\r\n\r\n            if (nextOnPathIndex === -1) {\r\n                // No more on-path points, we're done\r\n                break;\r\n            }\r\n\r\n            // Count off-path points between current and next on-path points\r\n            const offPathCount = nextOnPathIndex - i - 1;\r\n\r\n            if (offPathCount === 0) {\r\n                // Direct line segment\r\n                segments.push({\r\n                    type: 'line',\r\n                    points: [positions[i], positions[nextOnPathIndex]]\r\n                });\r\n            } else if (offPathCount === 1) {\r\n                // Quadratic curve (on, off, on)\r\n                segments.push({\r\n                    type: 'quadratic',\r\n                    points: [positions[i], positions[i + 1], positions[nextOnPathIndex]]\r\n                });\r\n            } else if (offPathCount === 2) {\r\n                // Cubic curve (on, off, off, on)\r\n                segments.push({\r\n                    type: 'cubic',\r\n                    points: [positions[i], positions[i + 1], positions[i + 2], positions[nextOnPathIndex]]\r\n                });\r\n            } else {\r\n                // More than 2 control points - treat as multiple segments\r\n                // This provides extensibility for complex curves\r\n                this.handleComplexSegment(segments, positions, i, nextOnPathIndex);\r\n            }\r\n\r\n            i = nextOnPathIndex;\r\n        }\r\n\r\n        return segments;\r\n    }\r\n\r\n    /**\r\n     * Find the next point that is on the path\r\n     */\r\n    private findNextOnPathPoint(pointBakers: PointBaker[], startIndex: number): number {\r\n        for (let i = startIndex; i < pointBakers.length; i++) {\r\n            if (pointBakers[i].isOnPath) {\r\n                return i;\r\n            }\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    /**\r\n     * Handle complex segments with more than 2 control points\r\n     * This provides extensibility for future curve types\r\n     */\r\n    private handleComplexSegment(segments: PathSegment[], positions: Vec3[], startIndex: number, endIndex: number): void {\r\n        // For now, break complex segments into multiple cubic curves\r\n        // This can be extended to support other curve types in the future\r\n\r\n        const controlPoints = [];\r\n        for (let i = startIndex + 1; i < endIndex; i++) {\r\n            controlPoints.push(positions[i]);\r\n        }\r\n\r\n        if (controlPoints.length >= 2) {\r\n            // Create cubic curves using pairs of control points\r\n            for (let i = 0; i < controlPoints.length - 1; i += 2) {\r\n                const start = i === 0 ? positions[startIndex] : controlPoints[i - 1];\r\n                const end = i + 2 >= controlPoints.length ? positions[endIndex] : controlPoints[i + 2];\r\n\r\n                segments.push({\r\n                    type: 'cubic',\r\n                    points: [start, controlPoints[i], controlPoints[i + 1], end]\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Draw all path segments\r\n     */\r\n    private drawPathSegments(graphics: Graphics, segments: PathSegment[]): void {\r\n        for (const segment of segments) {\r\n            switch (segment.type) {\r\n                case 'line':\r\n                    this.drawLineSegment(graphics, segment.points);\r\n                    break;\r\n                case 'quadratic':\r\n                    this.drawQuadraticSegment(graphics, segment.points);\r\n                    break;\r\n                case 'cubic':\r\n                    this.drawCubicSegment(graphics, segment.points);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Draw a single point when there's only one point in the path\r\n     */\r\n    private drawSinglePoint(graphics: Graphics, position: Vec3, isOnPath: boolean): void {\r\n        const color = isOnPath ? this.onPathPointColor : this.offPathPointColor;\r\n        GizmoUtils.drawCircle(graphics, position.x, position.y, this.pointRadius, color, isOnPath);\r\n    }\r\n\r\n    /**\r\n     * Draw a line segment between two points\r\n     */\r\n    private drawLineSegment(graphics: Graphics, points: Vec3[]): void {\r\n        if (points.length < 2) return;\r\n\r\n        const start = points[0];\r\n        const end = points[1];\r\n        GizmoUtils.drawLine(graphics, start.x, start.y, end.x, end.y, this.pathColor, this.lineWidth);\r\n    }\r\n\r\n    /**\r\n     * Draw a quadratic bezier curve\r\n     */\r\n    private drawQuadraticSegment(graphics: Graphics, points: Vec3[]): void {\r\n        if (points.length < 3) return;\r\n\r\n        const start = points[0];\r\n        const control = points[1];\r\n        const end = points[2];\r\n\r\n        GizmoUtils.drawQuadraticBezier(\r\n            graphics,\r\n            start.x, start.y,\r\n            control.x, control.y,\r\n            end.x, end.y,\r\n            this.pathColor,\r\n            20,\r\n            this.lineWidth\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Draw a cubic bezier curve\r\n     */\r\n    private drawCubicSegment(graphics: Graphics, points: Vec3[]): void {\r\n        if (points.length < 4) return;\r\n\r\n        const start = points[0];\r\n        const control1 = points[1];\r\n        const control2 = points[2];\r\n        const end = points[3];\r\n\r\n        GizmoUtils.drawCubicBezier(\r\n            graphics,\r\n            start.x, start.y,\r\n            control1.x, control1.y,\r\n            control2.x, control2.y,\r\n            end.x, end.y,\r\n            this.pathColor,\r\n            30,\r\n            this.lineWidth\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Draw control elements (points and handles)\r\n     */\r\n    private drawControlElements(graphics: Graphics, positions: Vec3[], pointBakers: PointBaker[], segments: PathSegment[]): void {\r\n        // Draw all points\r\n        for (let i = 0; i < positions.length; i++) {\r\n            const position = positions[i];\r\n            const isOnPath = pointBakers[i].isOnPath;\r\n            const color = isOnPath ? this.onPathPointColor : this.offPathPointColor;\r\n\r\n            GizmoUtils.drawCircle(graphics, position.x, position.y, this.pointRadius, color, isOnPath);\r\n        }\r\n\r\n        // Draw control handles for curves\r\n        this.drawControlHandles(graphics, positions, pointBakers, segments);\r\n    }\r\n\r\n    /**\r\n     * Draw control handles connecting control points to their anchor points\r\n     */\r\n    private drawControlHandles(graphics: Graphics, positions: Vec3[], pointBakers: PointBaker[], segments: PathSegment[]): void {\r\n        for (const segment of segments) {\r\n            if (segment.type === 'quadratic' && segment.points.length >= 3) {\r\n                // Draw handles from control point to both anchor points\r\n                const start = segment.points[0];\r\n                const control = segment.points[1];\r\n                const end = segment.points[2];\r\n\r\n                GizmoUtils.drawLine(graphics, start.x, start.y, control.x, control.y, this.controlPointColor, this.controlLineWidth);\r\n                GizmoUtils.drawLine(graphics, control.x, control.y, end.x, end.y, this.controlPointColor, this.controlLineWidth);\r\n\r\n            } else if (segment.type === 'cubic' && segment.points.length >= 4) {\r\n                // Draw handles from each control point to its nearest anchor point\r\n                const start = segment.points[0];\r\n                const control1 = segment.points[1];\r\n                const control2 = segment.points[2];\r\n                const end = segment.points[3];\r\n\r\n                GizmoUtils.drawLine(graphics, start.x, start.y, control1.x, control1.y, this.controlPointColor, this.controlLineWidth);\r\n                GizmoUtils.drawLine(graphics, control2.x, control2.y, end.x, end.y, this.controlPointColor, this.controlLineWidth);\r\n            }\r\n        }\r\n    }\r\n}"]}