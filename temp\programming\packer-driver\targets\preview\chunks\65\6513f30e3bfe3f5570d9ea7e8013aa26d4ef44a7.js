System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, PathPoint, PointBaked, _dec, _class2, _crd, ccclass, property, PointBaker;

  function _reportPossibleCrUseOfIBaker(extras) {
    _reporterNs.report("IBaker", "./Baker", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIBaked(extras) {
    _reporterNs.report("IBaked", "./Baker", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "../Data/PathPoint", _context.meta, extras);
  }

  _export("PointBaked", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      PathPoint = _unresolved_2.PathPoint;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a11f8BZqM9Esb3rg3Umfv1k", "PointBaker", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      // TODO: 导出json
      _export("PointBaked", PointBaked = class PointBaked {
        constructor() {
          this.points = void 0;
        }

      });

      _export("PointBaker", PointBaker = (_dec = ccclass('PointBaker'), _dec(_class2 = class PointBaker extends Component {
        constructor() {
          super(...arguments);
          this.isOnPath = false;
        }

        bake() {
          var baked = new PointBaked();
          baked.points = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(this.node.position.x, this.node.position.y, this.isOnPath);
          return baked;
        }

      }) || _class2));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6513f30e3bfe3f5570d9ea7e8013aa26d4ef44a7.js.map