{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts"], "names": ["PointBaked", "_decorator", "Component", "PathPoint", "ccclass", "property", "points", "PointBaker", "isOnPath", "bake", "baked", "node", "position", "x", "y"], "mappings": ";;;iHAOaA,U;;;;;;;;;;;;;;;;;;;;;;;AANJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AAGZC,MAAAA,S,iBAAAA,S;;;;;;;;;OAFH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;AAI9B;4BACaD,U,GAAN,MAAMA,UAAN,CAAmC;AAAA;AAAA,eACtCM,MADsC;AAAA;;AAAA,O;;4BAK7BC,U,WADZH,OAAO,CAAC,YAAD,C,iBAAR,MACaG,UADb,SACgCL,SADhC,CAEA;AAAA;AAAA;AAAA,eACWM,QADX,GAC+B,KAD/B;AAAA;;AAGIC,QAAAA,IAAI,GAAW;AACX,gBAAMC,KAAK,GAAG,IAAIV,UAAJ,EAAd;AACAU,UAAAA,KAAK,CAACJ,MAAN,GAAe;AAAA;AAAA,sCAAc,KAAKK,IAAL,CAAUC,QAAV,CAAmBC,CAAjC,EAAoC,KAAKF,IAAL,CAAUC,QAAV,CAAmBE,CAAvD,EAA0D,KAAKN,QAA/D,CAAf;AACA,iBAAOE,KAAP;AACH;;AAPL,O", "sourcesContent": ["\r\nimport { _decorator, Component, Node } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\nimport { IBaker, IBaked } from './Baker';\r\nimport { PathPoint } from '../Data/PathPoint';\r\n\r\n// TODO: 导出json\r\nexport class PointBaked implements IBaked {\r\n    points: PathPoint;\r\n}\r\n\r\n@ccclass('PointBaker')\r\nexport class PointBaker extends Component implements IBaker \r\n{\r\n    public isOnPath: boolean = false;\r\n\r\n    bake(): IBaked {\r\n        const baked = new PointBaked();\r\n        baked.points = new PathPoint(this.node.position.x, this.node.position.y, this.isOnPath);\r\n        return baked;\r\n    }\r\n}\r\n"]}