System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RegisterGizmoDrawer, PathBaker, _dec, _class, _crd, PathGizmo;

  function _reportPossibleCrUseOfGizmoDrawer(extras) {
    _reporterNs.report("GizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRegisterGizmoDrawer(extras) {
    _reporterNs.report("RegisterGizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathBaker(extras) {
    _reporterNs.report("PathBaker", "../world/level/Baker/PathBaker", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }, function (_unresolved_2) {
      GizmoDrawer = _unresolved_2.GizmoDrawer;
      RegisterGizmoDrawer = _unresolved_2.RegisterGizmoDrawer;
    }, function (_unresolved_3) {
      PathBaker = _unresolved_3.PathBaker;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e52daCSwV1GXYyHnMe5ogff", "PathGizmo", undefined);

      __checkObsolete__(['_decorator', 'Graphics', 'Color', 'Node', 'Vec3']);

      _export("PathGizmo", PathGizmo = (_dec = _crd && RegisterGizmoDrawer === void 0 ? (_reportPossibleCrUseOfRegisterGizmoDrawer({
        error: Error()
      }), RegisterGizmoDrawer) : RegisterGizmoDrawer, _dec(_class = class PathGizmo extends (_crd && GizmoDrawer === void 0 ? (_reportPossibleCrUseOfGizmoDrawer({
        error: Error()
      }), GizmoDrawer) : GizmoDrawer) {
        constructor(...args) {
          super(...args);
          this.componentType = _crd && PathBaker === void 0 ? (_reportPossibleCrUseOfPathBaker({
            error: Error()
          }), PathBaker) : PathBaker;
          this.drawerName = "PathGizmo";
        }

        drawGizmos(component, graphics, node) {
          const points = component.points;

          if (!points || points.length === 0) {
            return; // No points to draw
          }

          for (let i = 0; i < points.length; i++) {
            const point = points[i];
            const nextPoint = points[i + 1];
            const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);
            const gizmoX = gizmoPos.x + point.node.position.x;
            const gizmoY = gizmoPos.y + point.node.position.y; // draw the connection line to the next point based on whether it is on the path or not
            // we need to draw cubic or quadratic bezier curves

            if (nextPoint) {
              const nextGizmoPos = this.worldToGizmoSpace(nextPoint.node.worldPosition, graphics.node);
              const nextGizmoX = nextGizmoPos.x + nextPoint.node.position.x;
              const nextGizmoY = nextGizmoPos.y + nextPoint.node.position.y; // Draw line between points

              graphics.moveTo(gizmoX, gizmoY);
              graphics.lineTo(nextGizmoX, nextGizmoY);
            }
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=097ff54b16ee4025b9dc2a378ad676cdbf3c4f08.js.map