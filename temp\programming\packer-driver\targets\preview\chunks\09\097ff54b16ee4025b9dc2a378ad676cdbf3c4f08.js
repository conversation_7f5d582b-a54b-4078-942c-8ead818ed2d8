System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Color, Vec3, GizmoDrawer, RegisterGizmoDrawer, GizmoUtils, PathBaker, _dec, _class, _crd, PathGizmo;

  function _reportPossibleCrUseOfGizmoDrawer(extras) {
    _reporterNs.report("GizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRegisterGizmoDrawer(extras) {
    _reporterNs.report("RegisterGizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGizmoUtils(extras) {
    _reporterNs.report("GizmoUtils", "./GizmoUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathBaker(extras) {
    _reporterNs.report("PathBaker", "../world/level/Baker/PathBaker", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPointBaker(extras) {
    _reporterNs.report("PointBaker", "../world/level/Baker/PointBaker", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Color = _cc.Color;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      GizmoDrawer = _unresolved_2.GizmoDrawer;
      RegisterGizmoDrawer = _unresolved_2.RegisterGizmoDrawer;
    }, function (_unresolved_3) {
      GizmoUtils = _unresolved_3.GizmoUtils;
    }, function (_unresolved_4) {
      PathBaker = _unresolved_4.PathBaker;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e52daCSwV1GXYyHnMe5ogff", "PathGizmo", undefined);

      __checkObsolete__(['_decorator', 'Graphics', 'Color', 'Node', 'Vec3']);

      /**
       * Represents a path segment with its type and associated points
       */
      _export("PathGizmo", PathGizmo = (_dec = _crd && RegisterGizmoDrawer === void 0 ? (_reportPossibleCrUseOfRegisterGizmoDrawer({
        error: Error()
      }), RegisterGizmoDrawer) : RegisterGizmoDrawer, _dec(_class = class PathGizmo extends (_crd && GizmoDrawer === void 0 ? (_reportPossibleCrUseOfGizmoDrawer({
        error: Error()
      }), GizmoDrawer) : GizmoDrawer) {
        constructor() {
          super(...arguments);
          this.componentType = _crd && PathBaker === void 0 ? (_reportPossibleCrUseOfPathBaker({
            error: Error()
          }), PathBaker) : PathBaker;
          this.drawerName = "PathGizmo";
          // Visual settings
          this.pathColor = Color.CYAN;
          this.controlPointColor = Color.YELLOW;
          this.onPathPointColor = Color.GREEN;
          this.offPathPointColor = Color.RED;
          this.lineWidth = 2;
          this.pointRadius = 4;
          this.controlLineWidth = 1;
        }

        drawGizmos(component, graphics, node) {
          try {
            var points = component.points;

            if (!points || points.length === 0) {
              return; // No points to draw
            } // Filter out invalid point bakers with detailed logging


            var validPoints = points.filter((pointBaker, index) => {
              if (!pointBaker) {
                console.warn("PathGizmo: Point at index " + index + " is null or undefined");
                return false;
              }

              if (!pointBaker.node) {
                console.warn("PathGizmo: Point at index " + index + " has no node reference");
                return false;
              }

              if (!pointBaker.node.isValid) {
                console.warn("PathGizmo: Point at index " + index + " has invalid node");
                return false;
              }

              return true;
            });

            if (validPoints.length === 0) {
              console.warn("PathGizmo: No valid points found out of " + points.length + " total points");
              return; // No valid points to draw
            } // Convert PointBaker array to world positions


            var worldPositions = this.getWorldPositions(validPoints, graphics);

            if (worldPositions.length === 0) {
              console.warn("PathGizmo: Failed to get world positions for " + validPoints.length + " valid points");
              return; // Failed to get positions
            }

            if (worldPositions.length === 1) {
              console.warn("PathGizmo: Only one point in path, drawing single point");
              return;
            } // Parse path into segments based on isOnPath flags


            var segments = this.parsePathSegments(worldPositions, validPoints); // Draw all segments

            this.drawPathSegments(graphics, segments); // Draw control points and handles
            //this.drawControlElements(graphics, worldPositions, validPoints, segments);
          } catch (error) {
            console.error("PathGizmo: Error in drawGizmos:", error);
          }
        }
        /**
         * Convert PointBaker components to world positions in gizmo space
         */


        getWorldPositions(pointBakers, graphics) {
          if (!pointBakers || pointBakers.length === 0) {
            return [];
          }

          return pointBakers.map((pointBaker, index) => {
            try {
              var worldPos = pointBaker.node.worldPosition; // Use the graphics node as reference for gizmo space conversion

              var gizmoPos = this.worldToGizmoSpace(worldPos, graphics.node);
              return new Vec3(gizmoPos.x, gizmoPos.y, 0);
            } catch (error) {
              console.warn("Error getting world position for point baker at index " + index + ":", error);
              return new Vec3(0, 0, 0);
            }
          });
        }
        /**
         * Parse the path into segments based on isOnPath flags
         * Rules:
         * - 2 consecutive on-path points = line segment
         * - 3 points (on, off, on) = quadratic curve
         * - 4 points (on, off, off, on) = cubic curve
         */


        parsePathSegments(positions, pointBakers) {
          var segments = [];
          var i = 0;

          while (i < pointBakers.length) {
            var currentPoint = pointBakers[i];

            if (!currentPoint.isOnPath) {
              // Skip off-path points that aren't part of a curve
              i++;
              continue;
            } // Find the next on-path point


            var nextOnPathIndex = this.findNextOnPathPoint(pointBakers, i + 1);

            if (nextOnPathIndex === -1) {
              // No more on-path points, we're done
              break;
            } // Count off-path points between current and next on-path points


            var offPathCount = nextOnPathIndex - i - 1;

            if (offPathCount === 0) {
              // Direct line segment
              segments.push({
                type: 'line',
                points: [positions[i], positions[nextOnPathIndex]]
              });
            } else if (offPathCount === 1) {
              // Quadratic curve (on, off, on)
              segments.push({
                type: 'quadratic',
                points: [positions[i], positions[i + 1], positions[nextOnPathIndex]]
              });
            } else if (offPathCount === 2) {
              // Cubic curve (on, off, off, on)
              segments.push({
                type: 'cubic',
                points: [positions[i], positions[i + 1], positions[i + 2], positions[nextOnPathIndex]]
              });
            } else {
              // More than 2 control points - treat as multiple segments
              // This provides extensibility for complex curves
              this.handleComplexSegment(segments, positions, i, nextOnPathIndex);
            }

            i = nextOnPathIndex;
          }

          return segments;
        }
        /**
         * Find the next point that is on the path
         */


        findNextOnPathPoint(pointBakers, startIndex) {
          for (var i = startIndex; i < pointBakers.length; i++) {
            if (pointBakers[i].isOnPath) {
              return i;
            }
          }

          return -1;
        }
        /**
         * Handle complex segments with more than 2 control points
         * This provides extensibility for future curve types
         */


        handleComplexSegment(segments, positions, startIndex, endIndex) {
          // For now, break complex segments into multiple cubic curves
          // This can be extended to support other curve types in the future
          var controlPoints = [];

          for (var i = startIndex + 1; i < endIndex; i++) {
            controlPoints.push(positions[i]);
          }

          if (controlPoints.length >= 2) {
            // Create cubic curves using pairs of control points
            for (var _i = 0; _i < controlPoints.length - 1; _i += 2) {
              var start = _i === 0 ? positions[startIndex] : controlPoints[_i - 1];
              var end = _i + 2 >= controlPoints.length ? positions[endIndex] : controlPoints[_i + 2];
              segments.push({
                type: 'cubic',
                points: [start, controlPoints[_i], controlPoints[_i + 1], end]
              });
            }
          }
        }
        /**
         * Draw all path segments
         */


        drawPathSegments(graphics, segments) {
          for (var segment of segments) {
            switch (segment.type) {
              case 'line':
                this.drawLineSegment(graphics, segment.points);
                break;

              case 'quadratic':
                this.drawQuadraticSegment(graphics, segment.points);
                break;

              case 'cubic':
                this.drawCubicSegment(graphics, segment.points);
                break;
            }
          }
        }
        /**
         * Draw a line segment between two points
         */


        drawLineSegment(graphics, points) {
          if (points.length < 2) return;
          var start = points[0];
          var end = points[1];
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawLine(graphics, start.x, start.y, end.x, end.y, this.pathColor, this.lineWidth);
        }
        /**
         * Draw a quadratic bezier curve
         */


        drawQuadraticSegment(graphics, points) {
          if (points.length < 3) return;
          var start = points[0];
          var control = points[1];
          var end = points[2];
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawQuadraticBezier(graphics, start.x, start.y, control.x, control.y, end.x, end.y, this.pathColor, 20, this.lineWidth);
        }
        /**
         * Draw a cubic bezier curve
         */


        drawCubicSegment(graphics, points) {
          if (points.length < 4) return;
          var start = points[0];
          var control1 = points[1];
          var control2 = points[2];
          var end = points[3];
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawCubicBezier(graphics, start.x, start.y, control1.x, control1.y, control2.x, control2.y, end.x, end.y, this.pathColor, 30, this.lineWidth);
        }
        /**
         * Draw control elements (points and handles)
         */


        drawControlElements(graphics, positions, pointBakers, segments) {
          // Draw all points
          for (var i = 0; i < positions.length; i++) {
            var position = positions[i];
            var isOnPath = pointBakers[i].isOnPath;
            var color = isOnPath ? this.onPathPointColor : this.offPathPointColor;
            (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
              error: Error()
            }), GizmoUtils) : GizmoUtils).drawCircle(graphics, position.x, position.y, this.pointRadius, color, isOnPath);
          } // Draw control handles for curves


          this.drawControlHandles(graphics, positions, pointBakers, segments);
        }
        /**
         * Draw control handles connecting control points to their anchor points
         */


        drawControlHandles(graphics, positions, pointBakers, segments) {
          for (var segment of segments) {
            if (segment.type === 'quadratic' && segment.points.length >= 3) {
              // Draw handles from control point to both anchor points
              var start = segment.points[0];
              var control = segment.points[1];
              var end = segment.points[2];
              (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
                error: Error()
              }), GizmoUtils) : GizmoUtils).drawLine(graphics, start.x, start.y, control.x, control.y, this.controlPointColor, this.controlLineWidth);
              (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
                error: Error()
              }), GizmoUtils) : GizmoUtils).drawLine(graphics, control.x, control.y, end.x, end.y, this.controlPointColor, this.controlLineWidth);
            } else if (segment.type === 'cubic' && segment.points.length >= 4) {
              // Draw handles from each control point to its nearest anchor point
              var _start = segment.points[0];
              var control1 = segment.points[1];
              var control2 = segment.points[2];
              var _end = segment.points[3];
              (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
                error: Error()
              }), GizmoUtils) : GizmoUtils).drawLine(graphics, _start.x, _start.y, control1.x, control1.y, this.controlPointColor, this.controlLineWidth);
              (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
                error: Error()
              }), GizmoUtils) : GizmoUtils).drawLine(graphics, control2.x, control2.y, _end.x, _end.y, this.controlPointColor, this.controlLineWidth);
            }
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=097ff54b16ee4025b9dc2a378ad676cdbf3c4f08.js.map