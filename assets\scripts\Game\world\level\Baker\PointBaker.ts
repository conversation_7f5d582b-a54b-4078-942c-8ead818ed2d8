
import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;
import { IBaker, IBaked } from './Baker';
import { PathPoint } from '../Data/PathPoint';

// TODO: 导出json
export class PointBaked implements IBaked {
    points: PathPoint;
}

@ccclass('PointBaker')
export class PointBaker extends Component implements IBaker 
{
    public isOnPath: boolean = false;

    bake(): IBaked {
        const baked = new PointBaked();
        baked.points = new PathPoint(this.node.position.x, this.node.position.y, this.isOnPath);
        return baked;
    }
}
