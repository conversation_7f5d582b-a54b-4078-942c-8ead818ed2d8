import { _decorator, Graphics, Color, Node, Vec3 } from 'cc';
import { Giz<PERSON><PERSON>rawer, RegisterGizmoDrawer } from './GizmoDrawer';
import { GizmoUtils } from './GizmoUtils';
import { PathBaker } from '../world/level/Baker/PathBaker';
import { PathPoint } from '../world/level/Data/PathPoint';
import { PointBaker } from '../world/level/Baker/PointBaker';

/**
 * Represents a path segment with its type and associated points
 */
interface PathSegment {
    type: 'line' | 'quadratic' | 'cubic';
    points: Vec3[];
}

@RegisterGizmoDrawer
export class PathGizmo extends GizmoDrawer<PathBaker> {

    public readonly componentType = PathBaker;
    public readonly drawerName = "PathGizmo";

    // Visual settings
    private readonly pathColor = Color.CYAN;
    private readonly controlPointColor = Color.YELLOW;
    private readonly onPathPointColor = Color.GREEN;
    private readonly offPathPointColor = Color.RED;
    private readonly lineWidth = 2;
    private readonly pointRadius = 4;
    private readonly controlLineWidth = 1;

    public drawGizmos(component: PathBaker, graphics: Graphics, node: Node): void {
        const points = component.points;

        if (!points || points.length === 0) {
            return; // No points to draw
        }

        // Convert PointBaker array to world positions
        const worldPositions = this.getWorldPositions(points);

        if (worldPositions.length < 2) {
            // Draw single point
            this.drawSinglePoint(graphics, worldPositions[0], points[0].isOnPath);
            return;
        }

        // Parse path into segments based on isOnPath flags
        const segments = this.parsePathSegments(worldPositions, points);

        // Draw all segments
        this.drawPathSegments(graphics, segments);

        // Draw control points and handles
        this.drawControlElements(graphics, worldPositions, points, segments);
    }

    /**
     * Convert PointBaker components to world positions in gizmo space
     */
    private getWorldPositions(pointBakers: PointBaker[]): Vec3[] {
        return pointBakers.map(pointBaker => {
            const worldPos = pointBaker.node.worldPosition;
            const gizmoPos = this.worldToGizmoSpace(worldPos, pointBakers[0].node.parent);
            return new Vec3(gizmoPos.x, gizmoPos.y, 0);
        });
    }

    /**
     * Parse the path into segments based on isOnPath flags
     * Rules:
     * - 2 consecutive on-path points = line segment
     * - 3 points (on, off, on) = quadratic curve
     * - 4 points (on, off, off, on) = cubic curve
     */
    private parsePathSegments(positions: Vec3[], pointBakers: PointBaker[]): PathSegment[] {
        const segments: PathSegment[] = [];
        let i = 0;

        while (i < pointBakers.length) {
            const currentPoint = pointBakers[i];

            if (!currentPoint.isOnPath) {
                // Skip off-path points that aren't part of a curve
                i++;
                continue;
            }

            // Find the next on-path point
            let nextOnPathIndex = this.findNextOnPathPoint(pointBakers, i + 1);

            if (nextOnPathIndex === -1) {
                // No more on-path points, we're done
                break;
            }

            // Count off-path points between current and next on-path points
            const offPathCount = nextOnPathIndex - i - 1;

            if (offPathCount === 0) {
                // Direct line segment
                segments.push({
                    type: 'line',
                    points: [positions[i], positions[nextOnPathIndex]]
                });
            } else if (offPathCount === 1) {
                // Quadratic curve (on, off, on)
                segments.push({
                    type: 'quadratic',
                    points: [positions[i], positions[i + 1], positions[nextOnPathIndex]]
                });
            } else if (offPathCount === 2) {
                // Cubic curve (on, off, off, on)
                segments.push({
                    type: 'cubic',
                    points: [positions[i], positions[i + 1], positions[i + 2], positions[nextOnPathIndex]]
                });
            } else {
                // More than 2 control points - treat as multiple segments
                // This provides extensibility for complex curves
                this.handleComplexSegment(segments, positions, i, nextOnPathIndex);
            }

            i = nextOnPathIndex;
        }

        return segments;
    }

    /**
     * Find the next point that is on the path
     */
    private findNextOnPathPoint(pointBakers: PointBaker[], startIndex: number): number {
        for (let i = startIndex; i < pointBakers.length; i++) {
            if (pointBakers[i].isOnPath) {
                return i;
            }
        }
        return -1;
    }

    /**
     * Handle complex segments with more than 2 control points
     * This provides extensibility for future curve types
     */
    private handleComplexSegment(segments: PathSegment[], positions: Vec3[], startIndex: number, endIndex: number): void {
        // For now, break complex segments into multiple cubic curves
        // This can be extended to support other curve types in the future

        const controlPoints = [];
        for (let i = startIndex + 1; i < endIndex; i++) {
            controlPoints.push(positions[i]);
        }

        if (controlPoints.length >= 2) {
            // Create cubic curves using pairs of control points
            for (let i = 0; i < controlPoints.length - 1; i += 2) {
                const start = i === 0 ? positions[startIndex] : controlPoints[i - 1];
                const end = i + 2 >= controlPoints.length ? positions[endIndex] : controlPoints[i + 2];

                segments.push({
                    type: 'cubic',
                    points: [start, controlPoints[i], controlPoints[i + 1], end]
                });
            }
        }
    }

    /**
     * Draw all path segments
     */
    private drawPathSegments(graphics: Graphics, segments: PathSegment[]): void {
        for (const segment of segments) {
            switch (segment.type) {
                case 'line':
                    this.drawLineSegment(graphics, segment.points);
                    break;
                case 'quadratic':
                    this.drawQuadraticSegment(graphics, segment.points);
                    break;
                case 'cubic':
                    this.drawCubicSegment(graphics, segment.points);
                    break;
            }
        }
    }

    /**
     * Draw a single point when there's only one point in the path
     */
    private drawSinglePoint(graphics: Graphics, position: Vec3, isOnPath: boolean): void {
        const color = isOnPath ? this.onPathPointColor : this.offPathPointColor;
        GizmoUtils.drawCircle(graphics, position.x, position.y, this.pointRadius, color, isOnPath);
    }

    /**
     * Draw a line segment between two points
     */
    private drawLineSegment(graphics: Graphics, points: Vec3[]): void {
        if (points.length < 2) return;

        const start = points[0];
        const end = points[1];
        GizmoUtils.drawLine(graphics, start.x, start.y, end.x, end.y, this.pathColor, this.lineWidth);
    }

    /**
     * Draw a quadratic bezier curve
     */
    private drawQuadraticSegment(graphics: Graphics, points: Vec3[]): void {
        if (points.length < 3) return;

        const start = points[0];
        const control = points[1];
        const end = points[2];

        GizmoUtils.drawQuadraticBezier(
            graphics,
            start.x, start.y,
            control.x, control.y,
            end.x, end.y,
            this.pathColor,
            20,
            this.lineWidth
        );
    }

    /**
     * Draw a cubic bezier curve
     */
    private drawCubicSegment(graphics: Graphics, points: Vec3[]): void {
        if (points.length < 4) return;

        const start = points[0];
        const control1 = points[1];
        const control2 = points[2];
        const end = points[3];

        GizmoUtils.drawCubicBezier(
            graphics,
            start.x, start.y,
            control1.x, control1.y,
            control2.x, control2.y,
            end.x, end.y,
            this.pathColor,
            30,
            this.lineWidth
        );
    }

    /**
     * Draw control elements (points and handles)
     */
    private drawControlElements(graphics: Graphics, positions: Vec3[], pointBakers: PointBaker[], segments: PathSegment[]): void {
        // Draw all points
        for (let i = 0; i < positions.length; i++) {
            const position = positions[i];
            const isOnPath = pointBakers[i].isOnPath;
            const color = isOnPath ? this.onPathPointColor : this.offPathPointColor;

            GizmoUtils.drawCircle(graphics, position.x, position.y, this.pointRadius, color, isOnPath);
        }

        // Draw control handles for curves
        this.drawControlHandles(graphics, positions, pointBakers, segments);
    }

    /**
     * Draw control handles connecting control points to their anchor points
     */
    private drawControlHandles(graphics: Graphics, positions: Vec3[], pointBakers: PointBaker[], segments: PathSegment[]): void {
        for (const segment of segments) {
            if (segment.type === 'quadratic' && segment.points.length >= 3) {
                // Draw handles from control point to both anchor points
                const start = segment.points[0];
                const control = segment.points[1];
                const end = segment.points[2];

                GizmoUtils.drawLine(graphics, start.x, start.y, control.x, control.y, this.controlPointColor, this.controlLineWidth);
                GizmoUtils.drawLine(graphics, control.x, control.y, end.x, end.y, this.controlPointColor, this.controlLineWidth);

            } else if (segment.type === 'cubic' && segment.points.length >= 4) {
                // Draw handles from each control point to its nearest anchor point
                const start = segment.points[0];
                const control1 = segment.points[1];
                const control2 = segment.points[2];
                const end = segment.points[3];

                GizmoUtils.drawLine(graphics, start.x, start.y, control1.x, control1.y, this.controlPointColor, this.controlLineWidth);
                GizmoUtils.drawLine(graphics, control2.x, control2.y, end.x, end.y, this.controlPointColor, this.controlLineWidth);
            }
        }
    }
}