import { _decorator, Graphics, Color, Node, Vec3 } from 'cc';
import { Giz<PERSON><PERSON>rawer, RegisterGizmoDrawer } from './GizmoDrawer';
import { GizmoUtils } from './GizmoUtils';
import { PathBaker } from '../world/level/Baker/PathBaker';
import { PathPoint } from '../world/level/Data/PathPoint';

@RegisterGizmoDrawer
export class PathGizmo extends GizmoDrawer<PathBaker> {

    public readonly componentType = PathBaker;
    public readonly drawerName = "PathGizmo";

    public drawGizmos(component: PathBaker, graphics: Graphics, node: Node): void {
        const points = component.points;

        if (!points || points.length === 0) {
            return; // No points to draw
        }

        for (let i = 0; i < points.length; i++) {
            const point = points[i];
            const nextPoint = points[i + 1];
            const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);
            const gizmoX = gizmoPos.x + point.node.position.x;
            const gizmoY = gizmoPos.y + point.node.position.y;

            // draw the connection line to the next point based on whether it is on the path or not
            // we need to draw cubic or quadratic bezier curves
            if (nextPoint) {
                const nextGizmoPos = this.worldToGizmoSpace(nextPoint.node.worldPosition, graphics.node);
                const nextGizmoX = nextGizmoPos.x + nextPoint.node.position.x;
                const nextGizmoY = nextGizmoPos.y + nextPoint.node.position.y;

                // Draw line between points
                graphics.moveTo(gizmoX, gizmoY);
                graphics.lineTo(nextGizmoX, nextGizmoY);
            }
        }
    }
}