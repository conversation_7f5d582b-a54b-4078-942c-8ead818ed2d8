{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts"], "names": ["_decorator", "ccclass", "property", "PathPoint", "type", "Number", "Boolean", "constructor", "x", "y", "isOnPath"], "mappings": ";;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U,GAE9B;;2BAEaG,S,WACRD,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEC;AAAR,OAAD,C,UAERH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEC;AAAR,OAAD,C,UAERH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEE;AAAR,OAAD,C,EANZL,O,qBAAD,MACaE,SADb,CACuB;AAQnBI,QAAAA,WAAW,CAACC,CAAD,EAAYC,CAAZ,EAAuBC,QAAvB,EAA0C;AAAA;;AAAA;;AAAA;;AACjD,eAAKF,CAAL,GAASA,CAAT;AACA,eAAKC,CAAL,GAASA,CAAT;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AACH;;AAZkB,O", "sourcesContent": ["\r\nimport { _decorator, Component, Node } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n// define a point struct with x, y and boolean isOnPath\r\n@ccclass\r\nexport class PathPoint {\r\n    @property({ type: Number })\r\n    public x: number;\r\n    @property({ type: Number })\r\n    public y: number;\r\n    @property({ type: Boolean })\r\n    public isOnPath: boolean;\r\n\r\n    constructor(x: number, y: number, isOnPath: boolean) {\r\n        this.x = x;\r\n        this.y = y;\r\n        this.isOnPath = isOnPath;\r\n    }\r\n}"]}