
import { _decorator, CCFloat, CCBoolean } from 'cc';
const { ccclass, property } = _decorator;

// define a point struct with x, y and boolean isOnPath
@ccclass
export class PathPoint {
    @property({ type: CCFloat })
    public x: number;
    @property({ type: CCFloat })
    public y: number;
    @property({ type: CCBoolean })
    public isOnPath: boolean;

    constructor(x: number, y: number, isOnPath: boolean) {
        this.x = x;
        this.y = y;
        this.isOnPath = isOnPath;
    }
}