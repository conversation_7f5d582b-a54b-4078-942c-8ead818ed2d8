
import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

// define a point struct with x, y and boolean isOnPath
@ccclass
export class PathPoint {
    @property({ type: Number })
    public x: number;
    @property({ type: Number })
    public y: number;
    @property({ type: Boolean })
    public isOnPath: boolean;

    constructor(x: number, y: number, isOnPath: boolean) {
        this.x = x;
        this.y = y;
        this.isOnPath = isOnPath;
    }
}