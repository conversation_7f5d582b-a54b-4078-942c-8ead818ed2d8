{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PathGizmo.ts"], "names": ["Color", "Vec3", "GizmoDrawer", "RegisterGizmoDrawer", "Gizmo<PERSON><PERSON>s", "PathBaker", "PathGizmo", "componentType", "drawerName", "pathColor", "CYAN", "controlPointColor", "YELLOW", "onPathPointColor", "GREEN", "offPathPointColor", "RED", "lineWidth", "pointRadius", "controlLineWidth", "drawGizmos", "component", "graphics", "node", "points", "length", "validPoints", "filter", "pointBaker", "index", "console", "warn", "<PERSON><PERSON><PERSON><PERSON>", "worldPositions", "getWorldPositions", "segments", "parsePathSegments", "drawPathSegments", "error", "pointBakers", "map", "worldPos", "worldPosition", "gizmoPos", "worldToGizmoSpace", "x", "y", "positions", "i", "currentPoint", "isOnPath", "nextOnPathIndex", "findNextOnPathPoint", "offPathCount", "push", "type", "handleComplexSegment", "startIndex", "endIndex", "controlPoints", "start", "end", "segment", "drawLineSegment", "drawQuadraticSegment", "drawCubicSegment", "drawLine", "control", "drawQuadraticBezier", "control1", "control2", "drawCubicBezier", "drawControlElements", "position", "color", "drawCircle", "drawControlHandles"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA+BA,MAAAA,K,OAAAA,K;AAAaC,MAAAA,I,OAAAA,I;;AACnCC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,mB,iBAAAA,mB;;AACbC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;AAIT;AACA;AACA;2BAOaC,S;;oEADb,MACaA,SADb;AAAA;AAAA,sCACsD;AAAA;AAAA;AAAA,eAElCC,aAFkC;AAAA;AAAA;AAAA,eAGlCC,UAHkC,GAGrB,WAHqB;AAKlD;AALkD,eAMjCC,SANiC,GAMrBT,KAAK,CAACU,IANe;AAAA,eAOjCC,iBAPiC,GAObX,KAAK,CAACY,MAPO;AAAA,eAQjCC,gBARiC,GAQdb,KAAK,CAACc,KARQ;AAAA,eASjCC,iBATiC,GASbf,KAAK,CAACgB,GATO;AAAA,eAUjCC,SAViC,GAUrB,CAVqB;AAAA,eAWjCC,WAXiC,GAWnB,CAXmB;AAAA,eAYjCC,gBAZiC,GAYd,CAZc;AAAA;;AAc3CC,QAAAA,UAAU,CAACC,SAAD,EAAuBC,QAAvB,EAA2CC,IAA3C,EAA6D;AAC1E,cAAI;AACA,kBAAMC,MAAM,GAAGH,SAAS,CAACG,MAAzB;;AAEA,gBAAI,CAACA,MAAD,IAAWA,MAAM,CAACC,MAAP,KAAkB,CAAjC,EAAoC;AAChC,qBADgC,CACxB;AACX,aALD,CAOA;;;AACA,kBAAMC,WAAW,GAAGF,MAAM,CAACG,MAAP,CAAc,CAACC,UAAD,EAAaC,KAAb,KAAuB;AACrD,kBAAI,CAACD,UAAL,EAAiB;AACbE,gBAAAA,OAAO,CAACC,IAAR,CAAc,6BAA4BF,KAAM,uBAAhD;AACA,uBAAO,KAAP;AACH;;AAED,kBAAI,CAACD,UAAU,CAACL,IAAhB,EAAsB;AAClBO,gBAAAA,OAAO,CAACC,IAAR,CAAc,6BAA4BF,KAAM,wBAAhD;AACA,uBAAO,KAAP;AACH;;AAED,kBAAI,CAACD,UAAU,CAACL,IAAX,CAAgBS,OAArB,EAA8B;AAC1BF,gBAAAA,OAAO,CAACC,IAAR,CAAc,6BAA4BF,KAAM,mBAAhD;AACA,uBAAO,KAAP;AACH;;AAED,qBAAO,IAAP;AACH,aAjBmB,CAApB;;AAmBA,gBAAIH,WAAW,CAACD,MAAZ,KAAuB,CAA3B,EAA8B;AAC1BK,cAAAA,OAAO,CAACC,IAAR,CAAc,2CAA0CP,MAAM,CAACC,MAAO,eAAtE;AACA,qBAF0B,CAElB;AACX,aA9BD,CAgCA;;;AACA,kBAAMQ,cAAc,GAAG,KAAKC,iBAAL,CAAuBR,WAAvB,EAAoCJ,QAApC,CAAvB;;AAEA,gBAAIW,cAAc,CAACR,MAAf,KAA0B,CAA9B,EAAiC;AAC7BK,cAAAA,OAAO,CAACC,IAAR,CAAc,gDAA+CL,WAAW,CAACD,MAAO,eAAhF;AACA,qBAF6B,CAErB;AACX;;AAED,gBAAIQ,cAAc,CAACR,MAAf,KAA0B,CAA9B,EAAiC;AAC7BK,cAAAA,OAAO,CAACC,IAAR,CAAc,yDAAd;AACA;AACH,aA3CD,CA6CA;;;AACA,kBAAMI,QAAQ,GAAG,KAAKC,iBAAL,CAAuBH,cAAvB,EAAuCP,WAAvC,CAAjB,CA9CA,CA+CA;;AACA,iBAAKW,gBAAL,CAAsBf,QAAtB,EAAgCa,QAAhC,EAhDA,CAiDA;AACA;AAEH,WApDD,CAoDE,OAAOG,KAAP,EAAc;AACZR,YAAAA,OAAO,CAACQ,KAAR,CAAe,iCAAf,EAAiDA,KAAjD;AACH;AACJ;AAED;AACJ;AACA;;;AACYJ,QAAAA,iBAAiB,CAACK,WAAD,EAA4BjB,QAA5B,EAAwD;AAC7E,cAAI,CAACiB,WAAD,IAAgBA,WAAW,CAACd,MAAZ,KAAuB,CAA3C,EAA8C;AAC1C,mBAAO,EAAP;AACH;;AAED,iBAAOc,WAAW,CAACC,GAAZ,CAAgB,CAACZ,UAAD,EAAaC,KAAb,KAAuB;AAC1C,gBAAI;AACA,oBAAMY,QAAQ,GAAGb,UAAU,CAACL,IAAX,CAAgBmB,aAAjC,CADA,CAEA;;AACA,oBAAMC,QAAQ,GAAG,KAAKC,iBAAL,CAAuBH,QAAvB,EAAiCnB,QAAQ,CAACC,IAA1C,CAAjB;AACA,qBAAO,IAAItB,IAAJ,CAAS0C,QAAQ,CAACE,CAAlB,EAAqBF,QAAQ,CAACG,CAA9B,EAAiC,CAAjC,CAAP;AACH,aALD,CAKE,OAAOR,KAAP,EAAc;AACZR,cAAAA,OAAO,CAACC,IAAR,CAAc,yDAAwDF,KAAM,GAA5E,EAAgFS,KAAhF;AACA,qBAAO,IAAIrC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAP;AACH;AACJ,WAVM,CAAP;AAWH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACYmC,QAAAA,iBAAiB,CAACW,SAAD,EAAoBR,WAApB,EAA8D;AACnF,gBAAMJ,QAAuB,GAAG,EAAhC;AACA,cAAIa,CAAC,GAAG,CAAR;;AAEA,iBAAOA,CAAC,GAAGT,WAAW,CAACd,MAAvB,EAA+B;AAC3B,kBAAMwB,YAAY,GAAGV,WAAW,CAACS,CAAD,CAAhC;;AAEA,gBAAI,CAACC,YAAY,CAACC,QAAlB,EAA4B;AACxB;AACAF,cAAAA,CAAC;AACD;AACH,aAP0B,CAS3B;;;AACA,gBAAIG,eAAe,GAAG,KAAKC,mBAAL,CAAyBb,WAAzB,EAAsCS,CAAC,GAAG,CAA1C,CAAtB;;AAEA,gBAAIG,eAAe,KAAK,CAAC,CAAzB,EAA4B;AACxB;AACA;AACH,aAf0B,CAiB3B;;;AACA,kBAAME,YAAY,GAAGF,eAAe,GAAGH,CAAlB,GAAsB,CAA3C;;AAEA,gBAAIK,YAAY,KAAK,CAArB,EAAwB;AACpB;AACAlB,cAAAA,QAAQ,CAACmB,IAAT,CAAc;AACVC,gBAAAA,IAAI,EAAE,MADI;AAEV/B,gBAAAA,MAAM,EAAE,CAACuB,SAAS,CAACC,CAAD,CAAV,EAAeD,SAAS,CAACI,eAAD,CAAxB;AAFE,eAAd;AAIH,aAND,MAMO,IAAIE,YAAY,KAAK,CAArB,EAAwB;AAC3B;AACAlB,cAAAA,QAAQ,CAACmB,IAAT,CAAc;AACVC,gBAAAA,IAAI,EAAE,WADI;AAEV/B,gBAAAA,MAAM,EAAE,CAACuB,SAAS,CAACC,CAAD,CAAV,EAAeD,SAAS,CAACC,CAAC,GAAG,CAAL,CAAxB,EAAiCD,SAAS,CAACI,eAAD,CAA1C;AAFE,eAAd;AAIH,aANM,MAMA,IAAIE,YAAY,KAAK,CAArB,EAAwB;AAC3B;AACAlB,cAAAA,QAAQ,CAACmB,IAAT,CAAc;AACVC,gBAAAA,IAAI,EAAE,OADI;AAEV/B,gBAAAA,MAAM,EAAE,CAACuB,SAAS,CAACC,CAAD,CAAV,EAAeD,SAAS,CAACC,CAAC,GAAG,CAAL,CAAxB,EAAiCD,SAAS,CAACC,CAAC,GAAG,CAAL,CAA1C,EAAmDD,SAAS,CAACI,eAAD,CAA5D;AAFE,eAAd;AAIH,aANM,MAMA;AACH;AACA;AACA,mBAAKK,oBAAL,CAA0BrB,QAA1B,EAAoCY,SAApC,EAA+CC,CAA/C,EAAkDG,eAAlD;AACH;;AAEDH,YAAAA,CAAC,GAAGG,eAAJ;AACH;;AAED,iBAAOhB,QAAP;AACH;AAED;AACJ;AACA;;;AACYiB,QAAAA,mBAAmB,CAACb,WAAD,EAA4BkB,UAA5B,EAAwD;AAC/E,eAAK,IAAIT,CAAC,GAAGS,UAAb,EAAyBT,CAAC,GAAGT,WAAW,CAACd,MAAzC,EAAiDuB,CAAC,EAAlD,EAAsD;AAClD,gBAAIT,WAAW,CAACS,CAAD,CAAX,CAAeE,QAAnB,EAA6B;AACzB,qBAAOF,CAAP;AACH;AACJ;;AACD,iBAAO,CAAC,CAAR;AACH;AAED;AACJ;AACA;AACA;;;AACYQ,QAAAA,oBAAoB,CAACrB,QAAD,EAA0BY,SAA1B,EAA6CU,UAA7C,EAAiEC,QAAjE,EAAyF;AACjH;AACA;AAEA,gBAAMC,aAAa,GAAG,EAAtB;;AACA,eAAK,IAAIX,CAAC,GAAGS,UAAU,GAAG,CAA1B,EAA6BT,CAAC,GAAGU,QAAjC,EAA2CV,CAAC,EAA5C,EAAgD;AAC5CW,YAAAA,aAAa,CAACL,IAAd,CAAmBP,SAAS,CAACC,CAAD,CAA5B;AACH;;AAED,cAAIW,aAAa,CAAClC,MAAd,IAAwB,CAA5B,EAA+B;AAC3B;AACA,iBAAK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGW,aAAa,CAAClC,MAAd,GAAuB,CAA3C,EAA8CuB,CAAC,IAAI,CAAnD,EAAsD;AAClD,oBAAMY,KAAK,GAAGZ,CAAC,KAAK,CAAN,GAAUD,SAAS,CAACU,UAAD,CAAnB,GAAkCE,aAAa,CAACX,CAAC,GAAG,CAAL,CAA7D;AACA,oBAAMa,GAAG,GAAGb,CAAC,GAAG,CAAJ,IAASW,aAAa,CAAClC,MAAvB,GAAgCsB,SAAS,CAACW,QAAD,CAAzC,GAAsDC,aAAa,CAACX,CAAC,GAAG,CAAL,CAA/E;AAEAb,cAAAA,QAAQ,CAACmB,IAAT,CAAc;AACVC,gBAAAA,IAAI,EAAE,OADI;AAEV/B,gBAAAA,MAAM,EAAE,CAACoC,KAAD,EAAQD,aAAa,CAACX,CAAD,CAArB,EAA0BW,aAAa,CAACX,CAAC,GAAG,CAAL,CAAvC,EAAgDa,GAAhD;AAFE,eAAd;AAIH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYxB,QAAAA,gBAAgB,CAACf,QAAD,EAAqBa,QAArB,EAAoD;AACxE,eAAK,MAAM2B,OAAX,IAAsB3B,QAAtB,EAAgC;AAC5B,oBAAQ2B,OAAO,CAACP,IAAhB;AACI,mBAAK,MAAL;AACI,qBAAKQ,eAAL,CAAqBzC,QAArB,EAA+BwC,OAAO,CAACtC,MAAvC;AACA;;AACJ,mBAAK,WAAL;AACI,qBAAKwC,oBAAL,CAA0B1C,QAA1B,EAAoCwC,OAAO,CAACtC,MAA5C;AACA;;AACJ,mBAAK,OAAL;AACI,qBAAKyC,gBAAL,CAAsB3C,QAAtB,EAAgCwC,OAAO,CAACtC,MAAxC;AACA;AATR;AAWH;AACJ;AAED;AACJ;AACA;;;AACYuC,QAAAA,eAAe,CAACzC,QAAD,EAAqBE,MAArB,EAA2C;AAC9D,cAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;AAEvB,gBAAMmC,KAAK,GAAGpC,MAAM,CAAC,CAAD,CAApB;AACA,gBAAMqC,GAAG,GAAGrC,MAAM,CAAC,CAAD,CAAlB;AACA;AAAA;AAAA,wCAAW0C,QAAX,CAAoB5C,QAApB,EAA8BsC,KAAK,CAACf,CAApC,EAAuCe,KAAK,CAACd,CAA7C,EAAgDe,GAAG,CAAChB,CAApD,EAAuDgB,GAAG,CAACf,CAA3D,EAA8D,KAAKrC,SAAnE,EAA8E,KAAKQ,SAAnF;AACH;AAED;AACJ;AACA;;;AACY+C,QAAAA,oBAAoB,CAAC1C,QAAD,EAAqBE,MAArB,EAA2C;AACnE,cAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;AAEvB,gBAAMmC,KAAK,GAAGpC,MAAM,CAAC,CAAD,CAApB;AACA,gBAAM2C,OAAO,GAAG3C,MAAM,CAAC,CAAD,CAAtB;AACA,gBAAMqC,GAAG,GAAGrC,MAAM,CAAC,CAAD,CAAlB;AAEA;AAAA;AAAA,wCAAW4C,mBAAX,CACI9C,QADJ,EAEIsC,KAAK,CAACf,CAFV,EAEae,KAAK,CAACd,CAFnB,EAGIqB,OAAO,CAACtB,CAHZ,EAGesB,OAAO,CAACrB,CAHvB,EAIIe,GAAG,CAAChB,CAJR,EAIWgB,GAAG,CAACf,CAJf,EAKI,KAAKrC,SALT,EAMI,EANJ,EAOI,KAAKQ,SAPT;AASH;AAED;AACJ;AACA;;;AACYgD,QAAAA,gBAAgB,CAAC3C,QAAD,EAAqBE,MAArB,EAA2C;AAC/D,cAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;AAEvB,gBAAMmC,KAAK,GAAGpC,MAAM,CAAC,CAAD,CAApB;AACA,gBAAM6C,QAAQ,GAAG7C,MAAM,CAAC,CAAD,CAAvB;AACA,gBAAM8C,QAAQ,GAAG9C,MAAM,CAAC,CAAD,CAAvB;AACA,gBAAMqC,GAAG,GAAGrC,MAAM,CAAC,CAAD,CAAlB;AAEA;AAAA;AAAA,wCAAW+C,eAAX,CACIjD,QADJ,EAEIsC,KAAK,CAACf,CAFV,EAEae,KAAK,CAACd,CAFnB,EAGIuB,QAAQ,CAACxB,CAHb,EAGgBwB,QAAQ,CAACvB,CAHzB,EAIIwB,QAAQ,CAACzB,CAJb,EAIgByB,QAAQ,CAACxB,CAJzB,EAKIe,GAAG,CAAChB,CALR,EAKWgB,GAAG,CAACf,CALf,EAMI,KAAKrC,SANT,EAOI,EAPJ,EAQI,KAAKQ,SART;AAUH;AAED;AACJ;AACA;;;AACYuD,QAAAA,mBAAmB,CAAClD,QAAD,EAAqByB,SAArB,EAAwCR,WAAxC,EAAmEJ,QAAnE,EAAkG;AACzH;AACA,eAAK,IAAIa,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,SAAS,CAACtB,MAA9B,EAAsCuB,CAAC,EAAvC,EAA2C;AACvC,kBAAMyB,QAAQ,GAAG1B,SAAS,CAACC,CAAD,CAA1B;AACA,kBAAME,QAAQ,GAAGX,WAAW,CAACS,CAAD,CAAX,CAAeE,QAAhC;AACA,kBAAMwB,KAAK,GAAGxB,QAAQ,GAAG,KAAKrC,gBAAR,GAA2B,KAAKE,iBAAtD;AAEA;AAAA;AAAA,0CAAW4D,UAAX,CAAsBrD,QAAtB,EAAgCmD,QAAQ,CAAC5B,CAAzC,EAA4C4B,QAAQ,CAAC3B,CAArD,EAAwD,KAAK5B,WAA7D,EAA0EwD,KAA1E,EAAiFxB,QAAjF;AACH,WARwH,CAUzH;;;AACA,eAAK0B,kBAAL,CAAwBtD,QAAxB,EAAkCyB,SAAlC,EAA6CR,WAA7C,EAA0DJ,QAA1D;AACH;AAED;AACJ;AACA;;;AACYyC,QAAAA,kBAAkB,CAACtD,QAAD,EAAqByB,SAArB,EAAwCR,WAAxC,EAAmEJ,QAAnE,EAAkG;AACxH,eAAK,MAAM2B,OAAX,IAAsB3B,QAAtB,EAAgC;AAC5B,gBAAI2B,OAAO,CAACP,IAAR,KAAiB,WAAjB,IAAgCO,OAAO,CAACtC,MAAR,CAAeC,MAAf,IAAyB,CAA7D,EAAgE;AAC5D;AACA,oBAAMmC,KAAK,GAAGE,OAAO,CAACtC,MAAR,CAAe,CAAf,CAAd;AACA,oBAAM2C,OAAO,GAAGL,OAAO,CAACtC,MAAR,CAAe,CAAf,CAAhB;AACA,oBAAMqC,GAAG,GAAGC,OAAO,CAACtC,MAAR,CAAe,CAAf,CAAZ;AAEA;AAAA;AAAA,4CAAW0C,QAAX,CAAoB5C,QAApB,EAA8BsC,KAAK,CAACf,CAApC,EAAuCe,KAAK,CAACd,CAA7C,EAAgDqB,OAAO,CAACtB,CAAxD,EAA2DsB,OAAO,CAACrB,CAAnE,EAAsE,KAAKnC,iBAA3E,EAA8F,KAAKQ,gBAAnG;AACA;AAAA;AAAA,4CAAW+C,QAAX,CAAoB5C,QAApB,EAA8B6C,OAAO,CAACtB,CAAtC,EAAyCsB,OAAO,CAACrB,CAAjD,EAAoDe,GAAG,CAAChB,CAAxD,EAA2DgB,GAAG,CAACf,CAA/D,EAAkE,KAAKnC,iBAAvE,EAA0F,KAAKQ,gBAA/F;AAEH,aATD,MASO,IAAI2C,OAAO,CAACP,IAAR,KAAiB,OAAjB,IAA4BO,OAAO,CAACtC,MAAR,CAAeC,MAAf,IAAyB,CAAzD,EAA4D;AAC/D;AACA,oBAAMmC,KAAK,GAAGE,OAAO,CAACtC,MAAR,CAAe,CAAf,CAAd;AACA,oBAAM6C,QAAQ,GAAGP,OAAO,CAACtC,MAAR,CAAe,CAAf,CAAjB;AACA,oBAAM8C,QAAQ,GAAGR,OAAO,CAACtC,MAAR,CAAe,CAAf,CAAjB;AACA,oBAAMqC,GAAG,GAAGC,OAAO,CAACtC,MAAR,CAAe,CAAf,CAAZ;AAEA;AAAA;AAAA,4CAAW0C,QAAX,CAAoB5C,QAApB,EAA8BsC,KAAK,CAACf,CAApC,EAAuCe,KAAK,CAACd,CAA7C,EAAgDuB,QAAQ,CAACxB,CAAzD,EAA4DwB,QAAQ,CAACvB,CAArE,EAAwE,KAAKnC,iBAA7E,EAAgG,KAAKQ,gBAArG;AACA;AAAA;AAAA,4CAAW+C,QAAX,CAAoB5C,QAApB,EAA8BgD,QAAQ,CAACzB,CAAvC,EAA0CyB,QAAQ,CAACxB,CAAnD,EAAsDe,GAAG,CAAChB,CAA1D,EAA6DgB,GAAG,CAACf,CAAjE,EAAoE,KAAKnC,iBAAzE,EAA4F,KAAKQ,gBAAjG;AACH;AACJ;AACJ;;AArTiD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node, Vec3 } from 'cc';\r\nimport { Giz<PERSON><PERSON>rawer, RegisterGizmoDrawer } from './GizmoDrawer';\r\nimport { GizmoUtils } from './GizmoUtils';\r\nimport { PathBaker } from '../world/level/Baker/PathBaker';\r\nimport { PathPoint } from '../world/level/Data/PathPoint';\r\nimport { PointBaker } from '../world/level/Baker/PointBaker';\r\n\r\n/**\r\n * Represents a path segment with its type and associated points\r\n */\r\ninterface PathSegment {\r\n    type: 'line' | 'quadratic' | 'cubic';\r\n    points: Vec3[];\r\n}\r\n\r\n@RegisterGizmoDrawer\r\nexport class PathGizmo extends GizmoDrawer<PathBaker> {\r\n\r\n    public readonly componentType = PathBaker;\r\n    public readonly drawerName = \"PathGizmo\";\r\n\r\n    // Visual settings\r\n    private readonly pathColor = Color.CYAN;\r\n    private readonly controlPointColor = Color.YELLOW;\r\n    private readonly onPathPointColor = Color.GREEN;\r\n    private readonly offPathPointColor = Color.RED;\r\n    private readonly lineWidth = 2;\r\n    private readonly pointRadius = 4;\r\n    private readonly controlLineWidth = 1;\r\n\r\n    public drawGizmos(component: PathBaker, graphics: Graphics, node: Node): void {\r\n        try {\r\n            const points = component.points;\r\n\r\n            if (!points || points.length === 0) {\r\n                return; // No points to draw\r\n            }\r\n\r\n            // Filter out invalid point bakers with detailed logging\r\n            const validPoints = points.filter((pointBaker, index) => {\r\n                if (!pointBaker) {\r\n                    console.warn(`PathGizmo: Point at index ${index} is null or undefined`);\r\n                    return false;\r\n                }\r\n\r\n                if (!pointBaker.node) {\r\n                    console.warn(`PathGizmo: Point at index ${index} has no node reference`);\r\n                    return false;\r\n                }\r\n\r\n                if (!pointBaker.node.isValid) {\r\n                    console.warn(`PathGizmo: Point at index ${index} has invalid node`);\r\n                    return false;\r\n                }\r\n\r\n                return true;\r\n            });\r\n\r\n            if (validPoints.length === 0) {\r\n                console.warn(`PathGizmo: No valid points found out of ${points.length} total points`);\r\n                return; // No valid points to draw\r\n            }\r\n\r\n            // Convert PointBaker array to world positions\r\n            const worldPositions = this.getWorldPositions(validPoints, graphics);\r\n\r\n            if (worldPositions.length === 0) {\r\n                console.warn(`PathGizmo: Failed to get world positions for ${validPoints.length} valid points`);\r\n                return; // Failed to get positions\r\n            }\r\n\r\n            if (worldPositions.length === 1) {\r\n                console.warn(`PathGizmo: Only one point in path, drawing single point`);\r\n                return;\r\n            }\r\n\r\n            // Parse path into segments based on isOnPath flags\r\n            const segments = this.parsePathSegments(worldPositions, validPoints);\r\n            // Draw all segments\r\n            this.drawPathSegments(graphics, segments);\r\n            // Draw control points and handles\r\n            //this.drawControlElements(graphics, worldPositions, validPoints, segments);\r\n\r\n        } catch (error) {\r\n            console.error(`PathGizmo: Error in drawGizmos:`, error);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Convert PointBaker components to world positions in gizmo space\r\n     */\r\n    private getWorldPositions(pointBakers: PointBaker[], graphics: Graphics): Vec3[] {\r\n        if (!pointBakers || pointBakers.length === 0) {\r\n            return [];\r\n        }\r\n\r\n        return pointBakers.map((pointBaker, index) => {\r\n            try {\r\n                const worldPos = pointBaker.node.worldPosition;\r\n                // Use the graphics node as reference for gizmo space conversion\r\n                const gizmoPos = this.worldToGizmoSpace(worldPos, graphics.node);\r\n                return new Vec3(gizmoPos.x, gizmoPos.y, 0);\r\n            } catch (error) {\r\n                console.warn(`Error getting world position for point baker at index ${index}:`, error);\r\n                return new Vec3(0, 0, 0);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Parse the path into segments based on isOnPath flags\r\n     * Rules:\r\n     * - 2 consecutive on-path points = line segment\r\n     * - 3 points (on, off, on) = quadratic curve\r\n     * - 4 points (on, off, off, on) = cubic curve\r\n     */\r\n    private parsePathSegments(positions: Vec3[], pointBakers: PointBaker[]): PathSegment[] {\r\n        const segments: PathSegment[] = [];\r\n        let i = 0;\r\n\r\n        while (i < pointBakers.length) {\r\n            const currentPoint = pointBakers[i];\r\n\r\n            if (!currentPoint.isOnPath) {\r\n                // Skip off-path points that aren't part of a curve\r\n                i++;\r\n                continue;\r\n            }\r\n\r\n            // Find the next on-path point\r\n            let nextOnPathIndex = this.findNextOnPathPoint(pointBakers, i + 1);\r\n\r\n            if (nextOnPathIndex === -1) {\r\n                // No more on-path points, we're done\r\n                break;\r\n            }\r\n\r\n            // Count off-path points between current and next on-path points\r\n            const offPathCount = nextOnPathIndex - i - 1;\r\n\r\n            if (offPathCount === 0) {\r\n                // Direct line segment\r\n                segments.push({\r\n                    type: 'line',\r\n                    points: [positions[i], positions[nextOnPathIndex]]\r\n                });\r\n            } else if (offPathCount === 1) {\r\n                // Quadratic curve (on, off, on)\r\n                segments.push({\r\n                    type: 'quadratic',\r\n                    points: [positions[i], positions[i + 1], positions[nextOnPathIndex]]\r\n                });\r\n            } else if (offPathCount === 2) {\r\n                // Cubic curve (on, off, off, on)\r\n                segments.push({\r\n                    type: 'cubic',\r\n                    points: [positions[i], positions[i + 1], positions[i + 2], positions[nextOnPathIndex]]\r\n                });\r\n            } else {\r\n                // More than 2 control points - treat as multiple segments\r\n                // This provides extensibility for complex curves\r\n                this.handleComplexSegment(segments, positions, i, nextOnPathIndex);\r\n            }\r\n\r\n            i = nextOnPathIndex;\r\n        }\r\n\r\n        return segments;\r\n    }\r\n\r\n    /**\r\n     * Find the next point that is on the path\r\n     */\r\n    private findNextOnPathPoint(pointBakers: PointBaker[], startIndex: number): number {\r\n        for (let i = startIndex; i < pointBakers.length; i++) {\r\n            if (pointBakers[i].isOnPath) {\r\n                return i;\r\n            }\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    /**\r\n     * Handle complex segments with more than 2 control points\r\n     * This provides extensibility for future curve types\r\n     */\r\n    private handleComplexSegment(segments: PathSegment[], positions: Vec3[], startIndex: number, endIndex: number): void {\r\n        // For now, break complex segments into multiple cubic curves\r\n        // This can be extended to support other curve types in the future\r\n\r\n        const controlPoints = [];\r\n        for (let i = startIndex + 1; i < endIndex; i++) {\r\n            controlPoints.push(positions[i]);\r\n        }\r\n\r\n        if (controlPoints.length >= 2) {\r\n            // Create cubic curves using pairs of control points\r\n            for (let i = 0; i < controlPoints.length - 1; i += 2) {\r\n                const start = i === 0 ? positions[startIndex] : controlPoints[i - 1];\r\n                const end = i + 2 >= controlPoints.length ? positions[endIndex] : controlPoints[i + 2];\r\n\r\n                segments.push({\r\n                    type: 'cubic',\r\n                    points: [start, controlPoints[i], controlPoints[i + 1], end]\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Draw all path segments\r\n     */\r\n    private drawPathSegments(graphics: Graphics, segments: PathSegment[]): void {\r\n        for (const segment of segments) {\r\n            switch (segment.type) {\r\n                case 'line':\r\n                    this.drawLineSegment(graphics, segment.points);\r\n                    break;\r\n                case 'quadratic':\r\n                    this.drawQuadraticSegment(graphics, segment.points);\r\n                    break;\r\n                case 'cubic':\r\n                    this.drawCubicSegment(graphics, segment.points);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Draw a line segment between two points\r\n     */\r\n    private drawLineSegment(graphics: Graphics, points: Vec3[]): void {\r\n        if (points.length < 2) return;\r\n\r\n        const start = points[0];\r\n        const end = points[1];\r\n        GizmoUtils.drawLine(graphics, start.x, start.y, end.x, end.y, this.pathColor, this.lineWidth);\r\n    }\r\n\r\n    /**\r\n     * Draw a quadratic bezier curve\r\n     */\r\n    private drawQuadraticSegment(graphics: Graphics, points: Vec3[]): void {\r\n        if (points.length < 3) return;\r\n\r\n        const start = points[0];\r\n        const control = points[1];\r\n        const end = points[2];\r\n\r\n        GizmoUtils.drawQuadraticBezier(\r\n            graphics,\r\n            start.x, start.y,\r\n            control.x, control.y,\r\n            end.x, end.y,\r\n            this.pathColor,\r\n            20,\r\n            this.lineWidth\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Draw a cubic bezier curve\r\n     */\r\n    private drawCubicSegment(graphics: Graphics, points: Vec3[]): void {\r\n        if (points.length < 4) return;\r\n\r\n        const start = points[0];\r\n        const control1 = points[1];\r\n        const control2 = points[2];\r\n        const end = points[3];\r\n\r\n        GizmoUtils.drawCubicBezier(\r\n            graphics,\r\n            start.x, start.y,\r\n            control1.x, control1.y,\r\n            control2.x, control2.y,\r\n            end.x, end.y,\r\n            this.pathColor,\r\n            30,\r\n            this.lineWidth\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Draw control elements (points and handles)\r\n     */\r\n    private drawControlElements(graphics: Graphics, positions: Vec3[], pointBakers: PointBaker[], segments: PathSegment[]): void {\r\n        // Draw all points\r\n        for (let i = 0; i < positions.length; i++) {\r\n            const position = positions[i];\r\n            const isOnPath = pointBakers[i].isOnPath;\r\n            const color = isOnPath ? this.onPathPointColor : this.offPathPointColor;\r\n\r\n            GizmoUtils.drawCircle(graphics, position.x, position.y, this.pointRadius, color, isOnPath);\r\n        }\r\n\r\n        // Draw control handles for curves\r\n        this.drawControlHandles(graphics, positions, pointBakers, segments);\r\n    }\r\n\r\n    /**\r\n     * Draw control handles connecting control points to their anchor points\r\n     */\r\n    private drawControlHandles(graphics: Graphics, positions: Vec3[], pointBakers: PointBaker[], segments: PathSegment[]): void {\r\n        for (const segment of segments) {\r\n            if (segment.type === 'quadratic' && segment.points.length >= 3) {\r\n                // Draw handles from control point to both anchor points\r\n                const start = segment.points[0];\r\n                const control = segment.points[1];\r\n                const end = segment.points[2];\r\n\r\n                GizmoUtils.drawLine(graphics, start.x, start.y, control.x, control.y, this.controlPointColor, this.controlLineWidth);\r\n                GizmoUtils.drawLine(graphics, control.x, control.y, end.x, end.y, this.controlPointColor, this.controlLineWidth);\r\n\r\n            } else if (segment.type === 'cubic' && segment.points.length >= 4) {\r\n                // Draw handles from each control point to its nearest anchor point\r\n                const start = segment.points[0];\r\n                const control1 = segment.points[1];\r\n                const control2 = segment.points[2];\r\n                const end = segment.points[3];\r\n\r\n                GizmoUtils.drawLine(graphics, start.x, start.y, control1.x, control1.y, this.controlPointColor, this.controlLineWidth);\r\n                GizmoUtils.drawLine(graphics, control2.x, control2.y, end.x, end.y, this.controlPointColor, this.controlLineWidth);\r\n            }\r\n        }\r\n    }\r\n}"]}