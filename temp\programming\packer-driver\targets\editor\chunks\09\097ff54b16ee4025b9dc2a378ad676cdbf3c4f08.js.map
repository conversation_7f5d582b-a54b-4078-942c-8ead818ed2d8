{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PathGizmo.ts"], "names": ["GizmoDrawer", "RegisterGizmoDrawer", "PathBaker", "PathGizmo", "componentType", "drawerName", "drawGizmos", "component", "graphics", "node", "points", "length", "i", "point", "nextPoint", "gizmoPos", "worldToGizmoSpace", "worldPosition", "gizmoX", "x", "position", "gizmoY", "y", "nextGizmoPos", "nextGizmoX", "nextGizmoY", "moveTo", "lineTo"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,mB,iBAAAA,mB;;AAEbC,MAAAA,S,iBAAAA,S;;;;;;;;;2BAIIC,S;;oEADb,MACaA,SADb;AAAA;AAAA,sCACsD;AAAA;AAAA;AAAA,eAElCC,aAFkC;AAAA;AAAA;AAAA,eAGlCC,UAHkC,GAGrB,WAHqB;AAAA;;AAK3CC,QAAAA,UAAU,CAACC,SAAD,EAAuBC,QAAvB,EAA2CC,IAA3C,EAA6D;AAC1E,gBAAMC,MAAM,GAAGH,SAAS,CAACG,MAAzB;;AAEA,cAAI,CAACA,MAAD,IAAWA,MAAM,CAACC,MAAP,KAAkB,CAAjC,EAAoC;AAChC,mBADgC,CACxB;AACX;;AAED,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACC,MAA3B,EAAmCC,CAAC,EAApC,EAAwC;AACpC,kBAAMC,KAAK,GAAGH,MAAM,CAACE,CAAD,CAApB;AACA,kBAAME,SAAS,GAAGJ,MAAM,CAACE,CAAC,GAAG,CAAL,CAAxB;AACA,kBAAMG,QAAQ,GAAG,KAAKC,iBAAL,CAAuBP,IAAI,CAACQ,aAA5B,EAA2CT,QAAQ,CAACC,IAApD,CAAjB;AACA,kBAAMS,MAAM,GAAGH,QAAQ,CAACI,CAAT,GAAaN,KAAK,CAACJ,IAAN,CAAWW,QAAX,CAAoBD,CAAhD;AACA,kBAAME,MAAM,GAAGN,QAAQ,CAACO,CAAT,GAAaT,KAAK,CAACJ,IAAN,CAAWW,QAAX,CAAoBE,CAAhD,CALoC,CAOpC;AACA;;AACA,gBAAIR,SAAJ,EAAe;AACX,oBAAMS,YAAY,GAAG,KAAKP,iBAAL,CAAuBF,SAAS,CAACL,IAAV,CAAeQ,aAAtC,EAAqDT,QAAQ,CAACC,IAA9D,CAArB;AACA,oBAAMe,UAAU,GAAGD,YAAY,CAACJ,CAAb,GAAiBL,SAAS,CAACL,IAAV,CAAeW,QAAf,CAAwBD,CAA5D;AACA,oBAAMM,UAAU,GAAGF,YAAY,CAACD,CAAb,GAAiBR,SAAS,CAACL,IAAV,CAAeW,QAAf,CAAwBE,CAA5D,CAHW,CAKX;;AACAd,cAAAA,QAAQ,CAACkB,MAAT,CAAgBR,MAAhB,EAAwBG,MAAxB;AACAb,cAAAA,QAAQ,CAACmB,MAAT,CAAgBH,UAAhB,EAA4BC,UAA5B;AACH;AACJ;AACJ;;AA/BiD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node, Vec3 } from 'cc';\r\nimport { Giz<PERSON><PERSON>rawer, RegisterGizmoDrawer } from './GizmoDrawer';\r\nimport { GizmoUtils } from './GizmoUtils';\r\nimport { PathBaker } from '../world/level/Baker/PathBaker';\r\nimport { PathPoint } from '../world/level/Data/PathPoint';\r\n\r\n@RegisterGizmoDrawer\r\nexport class PathGizmo extends GizmoDrawer<PathBaker> {\r\n\r\n    public readonly componentType = PathBaker;\r\n    public readonly drawerName = \"PathGizmo\";\r\n\r\n    public drawGizmos(component: PathBaker, graphics: Graphics, node: Node): void {\r\n        const points = component.points;\r\n\r\n        if (!points || points.length === 0) {\r\n            return; // No points to draw\r\n        }\r\n\r\n        for (let i = 0; i < points.length; i++) {\r\n            const point = points[i];\r\n            const nextPoint = points[i + 1];\r\n            const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);\r\n            const gizmoX = gizmoPos.x + point.node.position.x;\r\n            const gizmoY = gizmoPos.y + point.node.position.y;\r\n\r\n            // draw the connection line to the next point based on whether it is on the path or not\r\n            // we need to draw cubic or quadratic bezier curves\r\n            if (nextPoint) {\r\n                const nextGizmoPos = this.worldToGizmoSpace(nextPoint.node.worldPosition, graphics.node);\r\n                const nextGizmoX = nextGizmoPos.x + nextPoint.node.position.x;\r\n                const nextGizmoY = nextGizmoPos.y + nextPoint.node.position.y;\r\n\r\n                // Draw line between points\r\n                graphics.moveTo(gizmoX, gizmoY);\r\n                graphics.lineTo(nextGizmoX, nextGizmoY);\r\n            }\r\n        }\r\n    }\r\n}"]}