System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Vec3, G<PERSON><PERSON><PERSON><PERSON><PERSON>, GizmoDrawer, _crd, gizmoDrawerRegistry;

  /**
   * Decorator to automatically register a gizmo drawer
   * Usage: @RegisterGizmoDrawer class MyGizmo extends GizmoDrawer { ... }
   */
  function RegisterGizmoDrawer(constructor) {
    // Add to registry for auto-registration
    gizmoDrawerRegistry.push(constructor);
    return constructor;
  }
  /**
   * Get all registered gizmo drawer constructors
   */


  function getRegisteredGizmoDrawers() {
    return [...gizmoDrawerRegistry];
  }
  /**
   * Auto-register all decorated gizmo drawers to a GizmoManager
   * This function should be called from GizmoManager to avoid circular dependencies
   */


  function autoRegisterGizmoDrawers(registerFunction) {
    for (const DrawerConstructor of gizmoDrawerRegistry) {
      try {
        const drawer = new DrawerConstructor();
        registerFunction(drawer);
      } catch (error) {
        console.error(`Failed to auto-register gizmo drawer ${DrawerConstructor.name}:`, error);
      }
    }
  }
  /**
   * Abstract base class for drawing gizmos for specific component types
   * This is not a component itself, but a drawer that can be registered to GizmoManager
   */


  function _reportPossibleCrUseOfGizmoUtils(extras) {
    _reporterNs.report("GizmoUtils", "./GizmoUtils", _context.meta, extras);
  }

  _export({
    RegisterGizmoDrawer: RegisterGizmoDrawer,
    getRegisteredGizmoDrawers: getRegisteredGizmoDrawers,
    autoRegisterGizmoDrawers: autoRegisterGizmoDrawers,
    GizmoDrawer: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      GizmoUtils = _unresolved_2.GizmoUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0088dL1x5NJPINMIFyaUJEf", "GizmoDrawer", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Graphics', 'Color', 'Node', 'Vec3']);

      // Registry for auto-registration
      gizmoDrawerRegistry = [];

      _export("GizmoDrawer", GizmoDrawer = class GizmoDrawer {
        constructor() {
          /**
           * The component type this drawer handles
           */

          /**
           * Name of this gizmo drawer for debugging
           */

          /**
           * Whether this drawer is enabled
           */
          this.enabled = true;
        }
        /**
         * Draw gizmos for the given component
         * @param component The component to draw gizmos for
         * @param graphics The graphics component to draw with
         * @param node The node that contains the component
         */


        /**
         * Check if this drawer can handle the given component
         * @param component The component to check
         * @returns true if this drawer can handle the component
         */
        canHandle(component) {
          return component instanceof this.componentType;
        }
        /**
         * Called when the drawer is registered to the manager
         * Override this to perform any initialization
         */


        onRegister() {// Override in subclasses if needed
        }
        /**
         * Called when the drawer is unregistered from the manager
         * Override this to perform any cleanup
         */


        onUnregister() {// Override in subclasses if needed
        }
        /**
         * Get the priority of this drawer (higher priority draws last/on top)
         * Override this to change drawing order
         */


        getPriority() {
          return 0;
        }
        /**
         * Convert world position to gizmo graphics coordinate space
         * For 2D projects, this converts world coordinates to the local space of the graphics node
         */


        worldToGizmoSpace(worldPos, gizmoNode) {
          // Convert world position to local position of the gizmo graphics node
          const localPos = new Vec3();
          gizmoNode.inverseTransformPoint(localPos, worldPos);
          return {
            x: localPos.x,
            y: localPos.y
          };
        }
        /**
         * Helper method to draw a cross at the given position
         */


        drawCross(graphics, x, y, size, color) {
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawCross(graphics, x, y, size, color);
        }
        /**
         * Helper method to draw a circle
         */


        drawCircle(graphics, x, y, radius, color, filled = false) {
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawCircle(graphics, x, y, radius, color, filled);
        }
        /**
         * Helper method to draw an arrow
         */


        drawArrow(graphics, startX, startY, endX, endY, color, arrowSize = 8) {
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawArrow(graphics, startX, startY, endX, endY, color, arrowSize);
        }
        /**
         * Helper method to draw a line
         */


        drawLine(graphics, startX, startY, endX, endY, color, lineWidth = 1) {
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawLine(graphics, startX, startY, endX, endY, color, lineWidth);
        }
        /**
         * Helper method to draw a rectangle
         */


        drawRect(graphics, x, y, width, height, color, filled = false) {
          (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).drawRect(graphics, x, y, width, height, color, filled);
        }
        /**
         * Helper method to draw text (simple implementation)
         * Note: For more complex text rendering, consider using Label components
         */


        drawText(_graphics, _text, _x, _y, _color) {// This is a placeholder - in a real implementation you might want to use Label components
          // or a more sophisticated text rendering system
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=14575ba2e85edcf9562818cfbe6f354b7e2052ea.js.map