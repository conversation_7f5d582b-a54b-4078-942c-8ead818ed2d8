{"modules": {"cce:/internal/x/cc": {"mTimestamp": 5993.8421, "chunkId": "93ba276ea7b26ffcdc433fab14afc1ed6f05647b", "imports": [{"value": "cce:/internal/x/cc-fu/base", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 14}, "end": {"line": 1, "column": 42}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 14}, "end": {"line": 2, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl2", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 14}, "end": {"line": 3, "column": 48}}}, {"value": "cce:/internal/x/cc-fu/gfx-empty", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 14}, "end": {"line": 4, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgpu", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 14}, "end": {"line": 5, "column": 48}}}, {"value": "cce:/internal/x/cc-fu/3d", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 14}, "end": {"line": 6, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/animation", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 14}, "end": {"line": 7, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/skeletal-animation", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 14}, "end": {"line": 8, "column": 56}}}, {"value": "cce:/internal/x/cc-fu/2d", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 14}, "end": {"line": 9, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/sorting", "resolved": "__unresolved_9", "loc": {"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/rich-text", "resolved": "__unresolved_10", "loc": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/mask", "resolved": "__unresolved_11", "loc": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 42}}}, {"value": "cce:/internal/x/cc-fu/graphics", "resolved": "__unresolved_12", "loc": {"start": {"line": 13, "column": 14}, "end": {"line": 13, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/ui-skew", "resolved": "__unresolved_13", "loc": {"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/ui", "resolved": "__unresolved_14", "loc": {"start": {"line": 15, "column": 14}, "end": {"line": 15, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/affine-transform", "resolved": "__unresolved_15", "loc": {"start": {"line": 16, "column": 14}, "end": {"line": 16, "column": 54}}}, {"value": "cce:/internal/x/cc-fu/particle", "resolved": "__unresolved_16", "loc": {"start": {"line": 17, "column": 14}, "end": {"line": 17, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/particle-2d", "resolved": "__unresolved_17", "loc": {"start": {"line": 18, "column": 14}, "end": {"line": 18, "column": 49}}}, {"value": "cce:/internal/x/cc-fu/physics-framework", "resolved": "__unresolved_18", "loc": {"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 55}}}, {"value": "cce:/internal/x/cc-fu/physics-cannon", "resolved": "__unresolved_19", "loc": {"start": {"line": 20, "column": 14}, "end": {"line": 20, "column": 52}}}, {"value": "cce:/internal/x/cc-fu/physics-physx", "resolved": "__unresolved_20", "loc": {"start": {"line": 21, "column": 14}, "end": {"line": 21, "column": 51}}}, {"value": "cce:/internal/x/cc-fu/physics-ammo", "resolved": "__unresolved_21", "loc": {"start": {"line": 22, "column": 14}, "end": {"line": 22, "column": 50}}}, {"value": "cce:/internal/x/cc-fu/physics-builtin", "resolved": "__unresolved_22", "loc": {"start": {"line": 23, "column": 14}, "end": {"line": 23, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-framework", "resolved": "__unresolved_23", "loc": {"start": {"line": 24, "column": 14}, "end": {"line": 24, "column": 58}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-box2d-jsb", "resolved": "__unresolved_24", "loc": {"start": {"line": 25, "column": 14}, "end": {"line": 25, "column": 58}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-box2d", "resolved": "__unresolved_25", "loc": {"start": {"line": 26, "column": 14}, "end": {"line": 26, "column": 54}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-builtin", "resolved": "__unresolved_26", "loc": {"start": {"line": 27, "column": 14}, "end": {"line": 27, "column": 56}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-box2d-wasm", "resolved": "__unresolved_27", "loc": {"start": {"line": 28, "column": 14}, "end": {"line": 28, "column": 59}}}, {"value": "cce:/internal/x/cc-fu/intersection-2d", "resolved": "__unresolved_28", "loc": {"start": {"line": 29, "column": 14}, "end": {"line": 29, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/primitive", "resolved": "__unresolved_29", "loc": {"start": {"line": 30, "column": 14}, "end": {"line": 30, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/profiler", "resolved": "__unresolved_30", "loc": {"start": {"line": 31, "column": 14}, "end": {"line": 31, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/geometry-renderer", "resolved": "__unresolved_31", "loc": {"start": {"line": 32, "column": 14}, "end": {"line": 32, "column": 55}}}, {"value": "cce:/internal/x/cc-fu/audio", "resolved": "__unresolved_32", "loc": {"start": {"line": 33, "column": 14}, "end": {"line": 33, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/video", "resolved": "__unresolved_33", "loc": {"start": {"line": 34, "column": 14}, "end": {"line": 34, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/xr", "resolved": "__unresolved_34", "loc": {"start": {"line": 35, "column": 14}, "end": {"line": 35, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/light-probe", "resolved": "__unresolved_35", "loc": {"start": {"line": 36, "column": 14}, "end": {"line": 36, "column": 49}}}, {"value": "cce:/internal/x/cc-fu/terrain", "resolved": "__unresolved_36", "loc": {"start": {"line": 37, "column": 14}, "end": {"line": 37, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/webview", "resolved": "__unresolved_37", "loc": {"start": {"line": 38, "column": 14}, "end": {"line": 38, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/tween", "resolved": "__unresolved_38", "loc": {"start": {"line": 39, "column": 14}, "end": {"line": 39, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/tiled-map", "resolved": "__unresolved_39", "loc": {"start": {"line": 40, "column": 14}, "end": {"line": 40, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/vendor-google", "resolved": "__unresolved_40", "loc": {"start": {"line": 41, "column": 14}, "end": {"line": 41, "column": 51}}}, {"value": "cce:/internal/x/cc-fu/spine", "resolved": "__unresolved_41", "loc": {"start": {"line": 42, "column": 14}, "end": {"line": 42, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/dragon-bones", "resolved": "__unresolved_42", "loc": {"start": {"line": 43, "column": 14}, "end": {"line": 43, "column": 50}}}, {"value": "cce:/internal/x/cc-fu/custom-pipeline", "resolved": "__unresolved_43", "loc": {"start": {"line": 44, "column": 14}, "end": {"line": 44, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/custom-pipeline-post-process", "resolved": "__unresolved_44", "loc": {"start": {"line": 45, "column": 14}, "end": {"line": 45, "column": 66}}}, {"value": "cce:/internal/x/cc-fu/legacy-pipeline", "resolved": "__unresolved_45", "loc": {"start": {"line": 46, "column": 14}, "end": {"line": 46, "column": 53}}}], "type": "esm", "resolutions": [{"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/base"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl2"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-empty"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgpu"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/3d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/animation"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/skeletal-animation"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/sorting"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/rich-text"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/mask"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/graphics"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/ui-skew"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/ui"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/affine-transform"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/particle"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/particle-2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-framework"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-cannon"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-physx"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-ammo"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-builtin"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-framework"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-box2d-jsb"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-box2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-builtin"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-box2d-wasm"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/intersection-2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/primitive"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/profiler"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/geometry-renderer"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/audio"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/video"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/xr"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/light-probe"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/terrain"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/webview"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/tween"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/tiled-map"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/vendor-google"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/spine"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/dragon-bones"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/custom-pipeline"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/custom-pipeline-post-process"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/legacy-pipeline"}, "messages": []}]}, "cce:/internal/x/prerequisite-imports": {"mTimestamp": 5896222.1443, "chunkId": "6d8fd2b0177941b032ddc0733af48a561fb60657", "imports": [{"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts", "resolved": "__unresolved_0", "loc": {"start": {"line": 5, "column": 35}, "end": {"line": 5, "column": 174}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts", "resolved": "__unresolved_1", "loc": {"start": {"line": 5, "column": 190}, "end": {"line": 5, "column": 334}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts", "resolved": "__unresolved_2", "loc": {"start": {"line": 5, "column": 350}, "end": {"line": 5, "column": 498}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 514}, "end": {"line": 5, "column": 659}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 675}, "end": {"line": 5, "column": 814}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts", "resolved": "__unresolved_5", "loc": {"start": {"line": 5, "column": 830}, "end": {"line": 5, "column": 962}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts", "resolved": "__unresolved_6", "loc": {"start": {"line": 5, "column": 978}, "end": {"line": 5, "column": 1043}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js", "resolved": "__unresolved_7", "loc": {"start": {"line": 5, "column": 1059}, "end": {"line": 5, "column": 1123}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Data/Bag.ts", "resolved": "__unresolved_8", "loc": {"start": {"line": 5, "column": 1139}, "end": {"line": 5, "column": 1192}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Data/BaseInfo.ts", "resolved": "__unresolved_9", "loc": {"start": {"line": 5, "column": 1208}, "end": {"line": 5, "column": 1266}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Data/DataManager.ts", "resolved": "__unresolved_10", "loc": {"start": {"line": 5, "column": 1282}, "end": {"line": 5, "column": 1343}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Data/GameLevel.ts", "resolved": "__unresolved_11", "loc": {"start": {"line": 5, "column": 1359}, "end": {"line": 5, "column": 1418}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts", "resolved": "__unresolved_12", "loc": {"start": {"line": 5, "column": 1434}, "end": {"line": 5, "column": 1488}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Background.ts", "resolved": "__unresolved_13", "loc": {"start": {"line": 5, "column": 1504}, "end": {"line": 5, "column": 1564}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts", "resolved": "__unresolved_14", "loc": {"start": {"line": 5, "column": 1580}, "end": {"line": 5, "column": 1635}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts", "resolved": "__unresolved_15", "loc": {"start": {"line": 5, "column": 1651}, "end": {"line": 5, "column": 1712}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts", "resolved": "__unresolved_16", "loc": {"start": {"line": 5, "column": 1728}, "end": {"line": 5, "column": 1786}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts", "resolved": "__unresolved_17", "loc": {"start": {"line": 5, "column": 1802}, "end": {"line": 5, "column": 1867}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts", "resolved": "__unresolved_18", "loc": {"start": {"line": 5, "column": 1883}, "end": {"line": 5, "column": 1939}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts", "resolved": "__unresolved_19", "loc": {"start": {"line": 5, "column": 1955}, "end": {"line": 5, "column": 2010}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts", "resolved": "__unresolved_20", "loc": {"start": {"line": 5, "column": 2026}, "end": {"line": 5, "column": 2084}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts", "resolved": "__unresolved_21", "loc": {"start": {"line": 5, "column": 2100}, "end": {"line": 5, "column": 2154}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts", "resolved": "__unresolved_22", "loc": {"start": {"line": 5, "column": 2170}, "end": {"line": 5, "column": 2226}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts", "resolved": "__unresolved_23", "loc": {"start": {"line": 5, "column": 2242}, "end": {"line": 5, "column": 2304}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts", "resolved": "__unresolved_24", "loc": {"start": {"line": 5, "column": 2320}, "end": {"line": 5, "column": 2389}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts", "resolved": "__unresolved_25", "loc": {"start": {"line": 5, "column": 2405}, "end": {"line": 5, "column": 2481}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts", "resolved": "__unresolved_26", "loc": {"start": {"line": 5, "column": 2497}, "end": {"line": 5, "column": 2567}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts", "resolved": "__unresolved_27", "loc": {"start": {"line": 5, "column": 2583}, "end": {"line": 5, "column": 2652}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts", "resolved": "__unresolved_28", "loc": {"start": {"line": 5, "column": 2668}, "end": {"line": 5, "column": 2738}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts", "resolved": "__unresolved_29", "loc": {"start": {"line": 5, "column": 2754}, "end": {"line": 5, "column": 2831}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts", "resolved": "__unresolved_30", "loc": {"start": {"line": 5, "column": 2847}, "end": {"line": 5, "column": 2919}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts", "resolved": "__unresolved_31", "loc": {"start": {"line": 5, "column": 2935}, "end": {"line": 5, "column": 3003}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts", "resolved": "__unresolved_32", "loc": {"start": {"line": 5, "column": 3019}, "end": {"line": 5, "column": 3088}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoUtils.ts", "resolved": "__unresolved_33", "loc": {"start": {"line": 5, "column": 3104}, "end": {"line": 5, "column": 3171}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PathGizmo.ts", "resolved": "__unresolved_34", "loc": {"start": {"line": 5, "column": 3187}, "end": {"line": 5, "column": 3253}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PointGizmo.ts", "resolved": "__unresolved_35", "loc": {"start": {"line": 5, "column": 3269}, "end": {"line": 5, "column": 3336}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/index.ts", "resolved": "__unresolved_36", "loc": {"start": {"line": 5, "column": 3352}, "end": {"line": 5, "column": 3414}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts", "resolved": "__unresolved_37", "loc": {"start": {"line": 5, "column": 3430}, "end": {"line": 5, "column": 3495}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts", "resolved": "__unresolved_38", "loc": {"start": {"line": 5, "column": 3511}, "end": {"line": 5, "column": 3586}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/Object.ts", "resolved": "__unresolved_39", "loc": {"start": {"line": 5, "column": 3602}, "end": {"line": 5, "column": 3669}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts", "resolved": "__unresolved_40", "loc": {"start": {"line": 5, "column": 3685}, "end": {"line": 5, "column": 3752}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts", "resolved": "__unresolved_41", "loc": {"start": {"line": 5, "column": 3768}, "end": {"line": 5, "column": 3844}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts", "resolved": "__unresolved_42", "loc": {"start": {"line": 5, "column": 3860}, "end": {"line": 5, "column": 3927}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts", "resolved": "__unresolved_43", "loc": {"start": {"line": 5, "column": 3943}, "end": {"line": 5, "column": 4009}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/index.ts", "resolved": "__unresolved_44", "loc": {"start": {"line": 5, "column": 4025}, "end": {"line": 5, "column": 4086}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/Baker.ts", "resolved": "__unresolved_45", "loc": {"start": {"line": 5, "column": 4102}, "end": {"line": 5, "column": 4175}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/LevelBaker.ts", "resolved": "__unresolved_46", "loc": {"start": {"line": 5, "column": 4191}, "end": {"line": 5, "column": 4269}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PathBaker.ts", "resolved": "__unresolved_47", "loc": {"start": {"line": 5, "column": 4285}, "end": {"line": 5, "column": 4362}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts", "resolved": "__unresolved_48", "loc": {"start": {"line": 5, "column": 4378}, "end": {"line": 5, "column": 4456}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts", "resolved": "__unresolved_49", "loc": {"start": {"line": 5, "column": 4472}, "end": {"line": 5, "column": 4548}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts", "resolved": "__unresolved_50", "loc": {"start": {"line": 5, "column": 4564}, "end": {"line": 5, "column": 4637}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts", "resolved": "__unresolved_51", "loc": {"start": {"line": 5, "column": 4653}, "end": {"line": 5, "column": 4728}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Bullet.ts", "resolved": "__unresolved_52", "loc": {"start": {"line": 5, "column": 4744}, "end": {"line": 5, "column": 4813}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/BulletSystem.ts", "resolved": "__unresolved_53", "loc": {"start": {"line": 5, "column": 4829}, "end": {"line": 5, "column": 4904}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Emitter.ts", "resolved": "__unresolved_54", "loc": {"start": {"line": 5, "column": 4920}, "end": {"line": 5, "column": 4990}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/EmitterArc.ts", "resolved": "__unresolved_55", "loc": {"start": {"line": 5, "column": 5006}, "end": {"line": 5, "column": 5079}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Weapon.ts", "resolved": "__unresolved_56", "loc": {"start": {"line": 5, "column": 5095}, "end": {"line": 5, "column": 5164}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/WeaponSlot.ts", "resolved": "__unresolved_57", "loc": {"start": {"line": 5, "column": 5180}, "end": {"line": 5, "column": 5253}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/GameInstance.ts", "resolved": "__unresolved_58", "loc": {"start": {"line": 5, "column": 5269}, "end": {"line": 5, "column": 5326}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/IMgr.ts", "resolved": "__unresolved_59", "loc": {"start": {"line": 5, "column": 5342}, "end": {"line": 5, "column": 5391}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts", "resolved": "__unresolved_60", "loc": {"start": {"line": 5, "column": 5407}, "end": {"line": 5, "column": 5466}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/MainUI.ts", "resolved": "__unresolved_61", "loc": {"start": {"line": 5, "column": 5482}, "end": {"line": 5, "column": 5533}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Network/DevLoginData.ts", "resolved": "__unresolved_62", "loc": {"start": {"line": 5, "column": 5549}, "end": {"line": 5, "column": 5614}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts", "resolved": "__unresolved_63", "loc": {"start": {"line": 5, "column": 5630}, "end": {"line": 5, "column": 5689}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts", "resolved": "__unresolved_64", "loc": {"start": {"line": 5, "column": 5705}, "end": {"line": 5, "column": 5769}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts", "resolved": "__unresolved_65", "loc": {"start": {"line": 5, "column": 5785}, "end": {"line": 5, "column": 5851}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts", "resolved": "__unresolved_66", "loc": {"start": {"line": 5, "column": 5867}, "end": {"line": 5, "column": 5934}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/ButtonPlus.ts", "resolved": "__unresolved_67", "loc": {"start": {"line": 5, "column": 5950}, "end": {"line": 5, "column": 6033}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/DragButton.ts", "resolved": "__unresolved_68", "loc": {"start": {"line": 5, "column": 6049}, "end": {"line": 5, "column": 6132}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/List/List.ts", "resolved": "__unresolved_69", "loc": {"start": {"line": 5, "column": 6148}, "end": {"line": 5, "column": 6223}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/List/ListItem.ts", "resolved": "__unresolved_70", "loc": {"start": {"line": 5, "column": 6239}, "end": {"line": 5, "column": 6318}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/SelectList/uiSelect.ts", "resolved": "__unresolved_71", "loc": {"start": {"line": 5, "column": 6334}, "end": {"line": 5, "column": 6419}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/SelectList/uiSelectItem.ts", "resolved": "__unresolved_72", "loc": {"start": {"line": 5, "column": 6435}, "end": {"line": 5, "column": 6524}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/DevLoginUI.ts", "resolved": "__unresolved_73", "loc": {"start": {"line": 5, "column": 6540}, "end": {"line": 5, "column": 6598}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/LoadingUI.ts", "resolved": "__unresolved_74", "loc": {"start": {"line": 5, "column": 6614}, "end": {"line": 5, "column": 6671}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Main/BattleUI.ts", "resolved": "__unresolved_75", "loc": {"start": {"line": 5, "column": 6687}, "end": {"line": 5, "column": 6748}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Main/BottomUI.ts", "resolved": "__unresolved_76", "loc": {"start": {"line": 5, "column": 6764}, "end": {"line": 5, "column": 6825}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Main/PlaneUI.ts", "resolved": "__unresolved_77", "loc": {"start": {"line": 5, "column": 6841}, "end": {"line": 5, "column": 6901}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Main/ShopUI.ts", "resolved": "__unresolved_78", "loc": {"start": {"line": 5, "column": 6917}, "end": {"line": 5, "column": 6976}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Main/SkyIslandUI.ts", "resolved": "__unresolved_79", "loc": {"start": {"line": 5, "column": 6992}, "end": {"line": 5, "column": 7056}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Main/TalentUI.ts", "resolved": "__unresolved_80", "loc": {"start": {"line": 5, "column": 7072}, "end": {"line": 5, "column": 7133}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/Main/TopUI.ts", "resolved": "__unresolved_81", "loc": {"start": {"line": 5, "column": 7149}, "end": {"line": 5, "column": 7207}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts", "resolved": "__unresolved_82", "loc": {"start": {"line": 5, "column": 7223}, "end": {"line": 5, "column": 7276}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts", "resolved": "__unresolved_83", "loc": {"start": {"line": 5, "column": 7292}, "end": {"line": 5, "column": 7349}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.mjs?cjs=&original=.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Data/Bag.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Data/BaseInfo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Data/DataManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Data/GameLevel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Background.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PathGizmo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PointGizmo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/index.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/Object.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/index.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/Baker.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/LevelBaker.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PathBaker.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Bullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/BulletSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Emitter.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/EmitterArc.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Weapon.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/WeaponSlot.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/GameInstance.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/IMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/MainUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Network/DevLoginData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/ButtonPlus.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/DragButton.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/List/List.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/List/ListItem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/SelectList/uiSelect.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/SelectList/uiSelectItem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/DevLoginUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/LoadingUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/BattleUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/BottomUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/PlaneUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/ShopUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/SkyIslandUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/TalentUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/TopUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts": {"mTimestamp": {"mtime": 1752314493464.55, "uuid": "11f3130e-c08c-47bb-a209-71d114594e6d"}, "chunkId": "d208b01558c13077e4a9c9f1302dfbf2b2122e40", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": 31}}}, {"value": "./builtin-pipeline-settings", "resolved": "__unresolved_1", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 36}}}, {"value": "./builtin-pipeline-pass", "resolved": "__unresolved_2", "loc": {"start": {"line": 38, "column": 7}, "end": {"line": 38, "column": 32}}}, {"value": "./builtin-pipeline", "resolved": "__unresolved_3", "loc": {"start": {"line": 45, "column": 7}, "end": {"line": 45, "column": 27}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts": {"mTimestamp": {"mtime": 1752314493468.5576, "uuid": "6f94083c-fc92-438b-a15b-a20ec61666c7"}, "chunkId": "b37de22ab9dff6e9f979ab9e517f60b5b4ae4ba5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": 11}}}, {"value": "./builtin-pipeline-settings", "resolved": "__unresolved_1", "loc": {"start": {"line": 32, "column": 40}, "end": {"line": 32, "column": 69}}}, {"value": "cc/env", "loc": {"start": {"line": 34, "column": 23}, "end": {"line": 34, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts": {"mTimestamp": {"mtime": 1752314493469.5603, "uuid": "de1c2107-70c8-4021-8459-6399f24d01c6"}, "chunkId": "36278e5c964e5e2f737bca1654894a5a7b2a7063", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": 31}}}, {"value": "./builtin-pipeline-types", "resolved": "__unresolved_1", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts": {"mTimestamp": {"mtime": 1752314493469.5603, "uuid": "cbf30902-517f-40dc-af90-a550bac27cf1"}, "chunkId": "383c24386be9d9de15fc0c17a8951753b54d596a", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 31, "column": 41}, "end": {"line": 31, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts": {"mTimestamp": {"mtime": 1752314493470.56, "uuid": "ff9b0199-ce04-4cfe-86cc-6c719f08d6e4"}, "chunkId": "9846cefb9cb6e16313f2e5e80bbb689314385757", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 29, "column": 7}, "end": {"line": 29, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 31, "column": 30}, "end": {"line": 31, "column": 38}}}, {"value": "./builtin-pipeline-types", "resolved": "__unresolved_1", "loc": {"start": {"line": 36, "column": 7}, "end": {"line": 36, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts": {"mTimestamp": {"mtime": 1752314493593.2278, "uuid": "b2bd1fa7-8d7c-49c5-a158-df29a6d3a594"}, "chunkId": "49c387c7d23ec5c771c1bd713cdd20f77551061d", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 220}, "end": {"line": 1, "column": 224}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts": {"mTimestamp": {"mtime": 1753501233443.8508, "uuid": "da084ae1-6b5b-4d7a-94a6-887fdbb7e052"}, "chunkId": "1afdb977ae03ab577533081b369d65c5f421dc5a", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts": {"mTimestamp": {"mtime": 1753501233450.601, "uuid": "81097702-1c01-407d-aeb2-686f8e932e49"}, "chunkId": "dfc204b977d4344f0a5bc22fc1377aa6da31bb5d", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts": {"mTimestamp": {"mtime": 1754103639575.8865, "uuid": "5348a46b-267c-4023-8b84-3382eb48f3c8"}, "chunkId": "0a34faf9cb5d6c878a0460b1d91d2043833d1540", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./GamePersistNode", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": 51}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Background.ts": {"mTimestamp": {"mtime": 1753501233570.987, "uuid": "03439b13-5f90-4827-b94b-4a91188a5588"}, "chunkId": "f2351c12fef31ec43cb4aa7781de26c8a25c10f9", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts": {"mTimestamp": {"mtime": 1754103639576.9568, "uuid": "c5c20e85-6542-4c38-8f04-f74386d5ec9f"}, "chunkId": "fc2fbfb1f30b2c72dbfc2a98e4a25bc281106361", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 169}, "end": {"line": 1, "column": 173}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 33}}}, {"value": "./GamePersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 5, "column": 32}, "end": {"line": 5, "column": 51}}}, {"value": "./Player", "resolved": "__unresolved_3", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 33}}}, {"value": "../ResUpdate/audioManager", "resolved": "__unresolved_4", "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 56}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts": {"mTimestamp": {"mtime": 1754103639576.9568, "uuid": "c8821111-2211-493e-ba13-2fe76ae3fa1c"}, "chunkId": "04e35005b949273cf9021ee06cd2d1683a661e7c", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 151}, "end": {"line": 1, "column": 155}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./GamePersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 32}, "end": {"line": 4, "column": 51}}}, {"value": "./Player", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 33}}}, {"value": "../ResUpdate/audioManager", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 56}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts": {"mTimestamp": {"mtime": 1753501233574.144, "uuid": "382d6722-7526-4b22-8289-be056b91e1aa"}, "chunkId": "83e5ddb6ce44a461006c21ee690b042176de43bc", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 67}, "end": {"line": 1, "column": 71}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts": {"mTimestamp": {"mtime": 1753501233575.0334, "uuid": "ffc9acad-fc44-4a0b-8e0d-6670b6c4e175"}, "chunkId": "a4972beeaf0fcdc8cd851bda6f7366e427f80e52", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts": {"mTimestamp": {"mtime": 1754103639579.0366, "uuid": "279df947-049f-4830-9cf0-65d34ee16a7e"}, "chunkId": "dd956057b78450450753a6fd7a42c04d4d2cf3fa", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 136}, "end": {"line": 1, "column": 140}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./GamePersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 32}, "end": {"line": 4, "column": 51}}}, {"value": "./Player", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts": {"mTimestamp": {"mtime": 1754103639579.0366, "uuid": "226be14a-b41d-43c8-86b2-bee80a67eb84"}, "chunkId": "60294c05f2abdf37c082915c18db52bac54df813", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 56}, "end": {"line": 1, "column": 60}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./GamePersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 32}, "end": {"line": 4, "column": 51}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts": {"mTimestamp": {"mtime": 1753501233577.879, "uuid": "5d5384ab-499f-4033-bbc9-ee1934c2d0c5"}, "chunkId": "b7458b4de0432717cd8478f412172b72032ccba8", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 54}, "end": {"line": 1, "column": 58}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts": {"mTimestamp": {"mtime": 1753501233578.6836, "uuid": "402f76e8-a281-47a5-8c49-298291c49c53"}, "chunkId": "8aef75f8cf32d7d41c1396085863afa89f31c29e", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 82}, "end": {"line": 1, "column": 86}}}, {"value": "./factroy/AnimFactory", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 51}}}, {"value": "./factroy/EnemyBulletFactory", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 35}, "end": {"line": 3, "column": 65}}}, {"value": "./factroy/EnemyFactory", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 53}}}, {"value": "./factroy/GoodsFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 53}}}, {"value": "./factroy/PlayerBulletFactory", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 36}, "end": {"line": 7, "column": 67}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts": {"mTimestamp": {"mtime": 1754103639580.093, "uuid": "53eed65f-04fc-48a2-9b7a-a716c1a966bc"}, "chunkId": "1963789e13e93dc0b226ee06945183fa9841c823", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 90}, "end": {"line": 1, "column": 94}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./GamePersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 32}, "end": {"line": 4, "column": 51}}}, {"value": "../ResUpdate/audioManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 56}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts": {"mTimestamp": {"mtime": 1754103639580.093, "uuid": "5ac3438e-1947-45e8-b9b1-71747e7a1efd"}, "chunkId": "6f9d772ac3d8077fc881b5fdd9427f4a4c4d7eb2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 158}, "end": {"line": 1, "column": 162}}}, {"value": "./Enemy", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 31}}}, {"value": "./Global", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 33}}}, {"value": "./GamePersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 32}, "end": {"line": 5, "column": 51}}}, {"value": "../ResUpdate/audioManager", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 56}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts": {"mTimestamp": {"mtime": 1754103639581.1018, "uuid": "cdb002a1-a1b1-42c5-99bd-0032423f6753"}, "chunkId": "d95224f77434950531683a9785befc8381eb4ea1", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../GamePersistNode", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 32}, "end": {"line": 2, "column": 52}}}, {"value": "./GameFactory", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts": {"mTimestamp": {"mtime": 1754103639581.1018, "uuid": "82bdeebd-c187-4869-82f8-8a78ea38a888"}, "chunkId": "1a08f53eb63f00053ff256dfed4926f5de065ac5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../EnemyBullet", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 44}}}, {"value": "../Global", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 34}}}, {"value": "../GamePersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 32}, "end": {"line": 4, "column": 52}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts": {"mTimestamp": {"mtime": 1754103639583.2424, "uuid": "11ec996f-30de-45ce-bb37-825678216d22"}, "chunkId": "b7e073d4f71adda5b81bb49903e8b28d4f91bd73", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../Enemy", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 32}}}, {"value": "../Global", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 34}}}, {"value": "../GamePersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 32}, "end": {"line": 4, "column": 52}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts": {"mTimestamp": {"mtime": 1754103639584.2698, "uuid": "7c1a95a6-c72a-48b2-82c7-dc072b10d00e"}, "chunkId": "aa6eb8cc91431ea77014b48fb3d674c1429f5b35", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 60}, "end": {"line": 1, "column": 64}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts": {"mTimestamp": {"mtime": 1754103639584.2698, "uuid": "6a38b65e-437d-4341-af31-e037c890fed3"}, "chunkId": "12a638bfa0ac053a36d52cbdf1de8f32a2b8e292", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 34}}}, {"value": "../Goods", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 22}, "end": {"line": 3, "column": 32}}}, {"value": "../GamePersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 32}, "end": {"line": 4, "column": 52}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts": {"mTimestamp": {"mtime": 1754103639585.351, "uuid": "73373683-639f-498e-a8c0-871383257cbe"}, "chunkId": "c6647226b8d9e9c0ee5733fb8979105406bf5cc1", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 34}}}, {"value": "../GamePersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": 52}}}, {"value": "../PlayerBullet", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 46}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts": {"mTimestamp": {"mtime": 1753501232978.1726, "uuid": "f74927ff-9ef4-4ad4-9eab-d346a22327b8"}, "chunkId": "5c6177446fd2faa8713cfff72c3ae83754ed0f4f", "imports": [{"value": "cc"}, {"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts": {"mTimestamp": {"mtime": 1753501232981.965, "uuid": "5dedc284-0b4f-4ccc-b206-ceb4f9722bde"}, "chunkId": "323a8c5994f8b20e12f09cd4d2d8cd061836d514", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts": {"mTimestamp": {"mtime": 1753501232981.965, "uuid": "df9194ea-9f3d-4045-b7ca-264b71eac88b"}, "chunkId": "046d807525505511ecf05655a8c20c5edadc3779", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 46}, "end": {"line": 1, "column": 50}}}, {"value": "./LevelData", "resolved": "__unresolved_1", "loc": {"start": {"line": 325, "column": 29}, "end": {"line": 325, "column": 42}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts": {"mTimestamp": {"mtime": 1753501233045.09, "uuid": "ef6e93b2-fcf7-4ce8-acd1-bf45f1df18ae"}, "chunkId": "0955e02420ac15ed40b8db54be054d4910d64a16", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 99}, "end": {"line": 1, "column": 103}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts": {"mTimestamp": {"mtime": 1753502943495.434, "uuid": "01d18540-68c6-47a1-bbb5-61467b98c848"}, "chunkId": "f09c883c6761d0b2e61d95659bbef6664068e8b3", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 56}, "end": {"line": 1, "column": 60}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 69}, "end": {"line": 3, "column": 84}}}, {"value": "../core/LevelSerializer", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 55}, "end": {"line": 4, "column": 80}}}, {"value": "../runtime/LevelManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 54}}}, {"value": "../runtime/CameraManager", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 30}, "end": {"line": 6, "column": 56}}}, {"value": "../runtime/PathManager", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 52}}}, {"value": "../runtime/SpawnerSystem", "resolved": "__unresolved_6", "loc": {"start": {"line": 8, "column": 30}, "end": {"line": 8, "column": 56}}}, {"value": "../runtime/MapSystem", "resolved": "__unresolved_7", "loc": {"start": {"line": 9, "column": 26}, "end": {"line": 9, "column": 48}}}, {"value": "../events/EventSystem", "resolved": "__unresolved_8", "loc": {"start": {"line": 10, "column": 34}, "end": {"line": 10, "column": 57}}}, {"value": "./EditorGizmos", "resolved": "__unresolved_9", "loc": {"start": {"line": 11, "column": 29}, "end": {"line": 11, "column": 45}}}, {"value": "./PathEditor", "resolved": "__unresolved_10", "loc": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts": {"mTimestamp": {"mtime": 1753501233047.094, "uuid": "9dc7fc2a-7a6e-47c4-a475-d714192e0745"}, "chunkId": "074300ecc0a1bae55cf24a4aec5415031cf84a44", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 103}, "end": {"line": 1, "column": 107}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}, {"value": "../runtime/PathManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 52}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts": {"mTimestamp": {"mtime": 1753501233066.702, "uuid": "ebd41303-70f0-4209-ad1f-f17396ebc8da"}, "chunkId": "f4d15e8644794a11a4a7e1c1dfa4b31d41d64117", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 56}, "end": {"line": 3, "column": 71}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts": {"mTimestamp": {"mtime": 1753501233137.0144, "uuid": "06785874-c974-426e-be89-ba4067b4a8f3"}, "chunkId": "cc37ee62c4be7ae793f2051ec6537f09ddab9978", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 84}, "end": {"line": 1, "column": 88}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 44}, "end": {"line": 3, "column": 59}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts": {"mTimestamp": {"mtime": 1753501233138.1533, "uuid": "47600cae-087c-4c1f-8b45-624a94fecf8d"}, "chunkId": "0df2f44674d1ead79d50c4f9b190f10693a8e0a2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 76}, "end": {"line": 1, "column": 80}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 30}, "end": {"line": 3, "column": 45}}}, {"value": "./SubLevelComponent", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 55}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts": {"mTimestamp": {"mtime": 1753501233139.3005, "uuid": "5eff8507-48b1-4399-83b5-1d5d5662a94a"}, "chunkId": "98868adf6b6d83f62e89581abd0546ea4fb0d2a2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 107}, "end": {"line": 1, "column": 111}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts": {"mTimestamp": {"mtime": 1753501233140.446, "uuid": "493225a7-a8b1-46fa-84b4-2416f862e324"}, "chunkId": "08adf0622c3a529112a521f23a0d5a02b6531aba", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 63}, "end": {"line": 1, "column": 67}}}, {"value": "../core/LevelData", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 39}, "end": {"line": 2, "column": 58}}}, {"value": "../core/Types", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 41}, "end": {"line": 3, "column": 56}}}, {"value": "./MapLayerComponent", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 55}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts": {"mTimestamp": {"mtime": 1753501233140.446, "uuid": "9761fbae-3b42-46c0-af28-24f077ad3ecb"}, "chunkId": "1cf9c3dbb012936a26c5aedda09409cec475901c", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 26}, "end": {"line": 3, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts": {"mTimestamp": {"mtime": 1753501233141.6362, "uuid": "e4d55c74-27c7-48f9-80b8-0e06c64d792b"}, "chunkId": "d344f5ec5a8c39d8d3d05db943d295ef5a3aabd1", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 39}, "end": {"line": 3, "column": 54}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts": {"mTimestamp": {"mtime": 1753501233142.7844, "uuid": "8c3d2dbc-7bce-4770-a6a3-4cff3848caf8"}, "chunkId": "5850102fe022728d9bd4ccaa9aa26f8c28ede683", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 82}, "end": {"line": 1, "column": 86}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 40}, "end": {"line": 3, "column": 55}}}, {"value": "./PathFollower", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts": {"mTimestamp": {"mtime": 1753502413787.8794, "uuid": "adbfa057-f666-4e0b-86b3-e92e14d59d15"}, "chunkId": "7463a40fbec321b7410d64dfcdc1b3e6c62658cc", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 39}, "end": {"line": 3, "column": 54}}}, {"value": "./SpawnerComponent", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 33}, "end": {"line": 4, "column": 53}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts": {"mTimestamp": {"mtime": 1753501233145.1392, "uuid": "32e79263-806c-4849-8282-5ea416efb23b"}, "chunkId": "3ac4b7632cf277a4233524a4002d4cd334bbeaaf", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 64}, "end": {"line": 1, "column": 68}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 41}, "end": {"line": 3, "column": 56}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/TestComponent.ts": {"mTimestamp": {"mtime": 1753501233145.1392, "uuid": "b4ef553f-e7dc-4bd4-b3da-a1e1d1009bdd"}, "chunkId": "60b0742d864a6c139aedede62106ac6aad9d71ce", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/GameInstance.ts": {"mTimestamp": {"mtime": 1754103639669.593, "uuid": "815bc83a-9815-48da-859e-0a61c8d7764f"}, "chunkId": "6734c38c3959f7df5525dcbc49e6db21ed22087e", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}, {"value": "./Luban/LubanMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 43}}}, {"value": "./Network/NetMgr", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 41}}}, {"value": "./ResUpdate/audioManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 55}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/IMgr.ts": {"mTimestamp": {"mtime": 1753501233600.3826, "uuid": "4f6cfd7a-7406-4eb5-a557-4e504e81c910"}, "chunkId": "c251e6d154e03d8cce491ae350d65095ae7f8c83", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts": {"mTimestamp": {"mtime": 1754103639670.5967, "uuid": "3d9905b2-c72b-4a59-84d9-ab422829ec3b"}, "chunkId": "17d3f4c55293eb211367452bdd4c17958b68e0ed", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 80}, "end": {"line": 1, "column": 84}}}, {"value": "../IMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 30}}}, {"value": "../AutoGen/Luban/schema", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 46}}}, {"value": "../Utils/Logger", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/IMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/MainUI.ts": {"mTimestamp": {"mtime": 1754103639677.7778, "uuid": "ae524ea8-f776-4444-9e5b-a5143e06dbf9"}, "chunkId": "a62bce7c65bfc0783f10bcc13fb7efd330e855ae", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 48}, "end": {"line": 1, "column": 52}}}, {"value": "./UI/Main/BattleUI", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 45}}}, {"value": "./UI/Main/BottomUI", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 45}}}, {"value": "./UI/Main/TopUI", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 22}, "end": {"line": 4, "column": 39}}}, {"value": "./UI/UIMgr", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 34}}}, {"value": "./GameInstance", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 45}}}, {"value": "./Utils/Logger", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 34}, "end": {"line": 7, "column": 50}}}, {"value": "./AutoGen/PB/cs_proto.js", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 46}}}, {"value": "./UI/DevLoginUI", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 44}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/BattleUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/BottomUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/TopUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/GameInstance.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.mjs?cjs=&original=.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/DevLoginUI.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts": {"mTimestamp": {"mtime": 1754103639679.787, "uuid": "d7f90c8a-8a20-40f9-9a4a-98f31574e17a"}, "chunkId": "adac3babafbcaaff82b91cc080fe88b86548f97a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 32}, "end": {"line": 1, "column": 36}}}, {"value": "../IMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 30}}}, {"value": "../AutoGen/PB/cs_proto.js", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 20}, "end": {"line": 4, "column": 47}}}, {"value": "crypto-js", "loc": {"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": 32}}}, {"value": "../Utils/Logger", "resolved": "__unresolved_3", "loc": {"start": {"line": 6, "column": 53}, "end": {"line": 6, "column": 70}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/IMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.mjs?cjs=&original=.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/index.mjs?cjs=&original=.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts": {"mTimestamp": {"mtime": 1754103639687.5127, "uuid": "30734ca9-59e8-4f53-9672-855d960d7427"}, "chunkId": "9d15e7d22d1a1328ffa5d899e41c0fefa70e2b52", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 93}, "end": {"line": 1, "column": 97}}}, {"value": "cc/env", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 31}}}, {"value": "../UI/UIMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 22}, "end": {"line": 3, "column": 35}}}, {"value": "../Utils/Logger", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 42}}}, {"value": "../UI/Main/BattleUI", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 46}}}, {"value": "../UI/Main/BottomUI", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 46}}}, {"value": "../UI/Main/TopUI", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 40}}}, {"value": "../UI/DevLoginUI", "resolved": "__unresolved_6", "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/BattleUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/BottomUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/TopUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/DevLoginUI.ts"}, "messages": []}]}, "cce:/internal/code-quality/cr.mjs": {"mTimestamp": 1754103703198, "chunkId": "6a5019a719a9014c047e67aa1cf34453ab8392ce", "imports": [], "type": "esm", "resolutions": []}, "file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts": {"mTimestamp": {"mtime": 1754103639258.5886, "uuid": "65d59376-f39a-4365-8dd7-250c1d7140db"}, "chunkId": "a880040b201a43078251d202cec131aa4b75a57a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./base/World", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 34}, "end": {"line": 2, "column": 48}}}, {"value": "./WorldInitializeData", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 85}, "end": {"line": 3, "column": 108}}}, {"value": "./level/LevelSystem", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 49}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts": {"mTimestamp": {"mtime": 1753523094308.763, "uuid": "0c36c604-d0de-4dc7-9fc7-132803bb9bf9"}, "chunkId": "385bceab2f32443e2046001b74fb95c3888d001b", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts": {"mTimestamp": {"mtime": 1754103639260.6462, "uuid": "68463dec-03c8-4483-a858-88be18da4675"}, "chunkId": "18115108ff656ee90e2863a8933b4707545ec1a5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "./TypeID", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 34}, "end": {"line": 2, "column": 44}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts": {"mTimestamp": {"mtime": 1754103639260.6462, "uuid": "237cafbd-51ce-4858-b19c-a058b48c9d0c"}, "chunkId": "da16bcc6584bc2ac43355704538ffd77fd8040f2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "./TypeID", "resolved": "__unresolved_1", "loc": {"start": {"line": 4, "column": 36}, "end": {"line": 4, "column": 46}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts": {"mTimestamp": {"mtime": 1754103639261.8855, "uuid": "0eea3cf5-1ec0-4055-99d5-ee74c4c53850"}, "chunkId": "fa2784ebc0eff50dc40a64b60e09f681dd051e69", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts": {"mTimestamp": {"mtime": 1754103639261.8855, "uuid": "73f5b7e0-b6c4-4bd2-807b-a090d20b7c1c"}, "chunkId": "22fd948e93501951a69c80979846d28b25223745", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "./SystemContainer", "resolved": "__unresolved_1", "loc": {"start": {"line": 1, "column": 32}, "end": {"line": 1, "column": 51}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts": {"mTimestamp": {"mtime": 1753523712679.3455, "uuid": "7cb83a17-5595-4c04-9619-63b94934fc55"}, "chunkId": "89e87d2ae93ede041b1e31f4df93a413b6e36cc2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 39}, "end": {"line": 1, "column": 43}}}, {"value": "../base/System", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 39}}}, {"value": "../base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/index.ts": {"mTimestamp": {"mtime": 1754103639507.756, "uuid": "22545f8c-666f-4136-8361-0fe5fba57a0f"}, "chunkId": "930745d09e5696807f7ac7587de3ae2f72795c1d", "imports": [{"value": "cc"}, {"value": "./base/System", "resolved": "__unresolved_0", "loc": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 38}}}, {"value": "./base/SystemContainer", "resolved": "__unresolved_1", "loc": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 56}}}, {"value": "./base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 12, "column": 50}, "end": {"line": 12, "column": 65}}}, {"value": "./base/TypeID", "resolved": "__unresolved_3", "loc": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 43}}}, {"value": "./base/Object", "resolved": "__unresolved_4", "loc": {"start": {"line": 15, "column": 24}, "end": {"line": 15, "column": 39}}}, {"value": "./base/World", "resolved": "__unresolved_5", "loc": {"start": {"line": 18, "column": 34}, "end": {"line": 18, "column": 48}}}, {"value": "./WorldInitializeData", "resolved": "__unresolved_6", "loc": {"start": {"line": 25, "column": 7}, "end": {"line": 25, "column": 30}}}, {"value": "./Bootstrap", "resolved": "__unresolved_7", "loc": {"start": {"line": 36, "column": 26}, "end": {"line": 36, "column": 39}}}, {"value": "./weapon/BulletSystem", "resolved": "__unresolved_8", "loc": {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 52}}}, {"value": "./weapon/Emitter", "resolved": "__unresolved_9", "loc": {"start": {"line": 43, "column": 24}, "end": {"line": 43, "column": 42}}}, {"value": "./weapon/EmitterArc", "resolved": "__unresolved_10", "loc": {"start": {"line": 44, "column": 27}, "end": {"line": 44, "column": 48}}}, {"value": "../gizmos", "resolved": "__unresolved_11", "loc": {"start": {"line": 47, "column": 14}, "end": {"line": 47, "column": 25}}}, {"value": "./level/LevelSystem", "resolved": "__unresolved_12", "loc": {"start": {"line": 49, "column": 44}, "end": {"line": 49, "column": 65}}}, {"value": "./player/PlayerSystem", "resolved": "__unresolved_13", "loc": {"start": {"line": 57, "column": 42}, "end": {"line": 57, "column": 65}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/Object.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/BulletSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Emitter.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/EmitterArc.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/index.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts": {"mTimestamp": {"mtime": 1753522973312.641, "uuid": "21f782d5-044f-45eb-b25d-c6291183b1ad"}, "chunkId": "c48055c8729e2ddd6c65e69f10e6be25f855116b", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 39}, "end": {"line": 1, "column": 43}}}, {"value": "../base/System", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 39}}}, {"value": "../base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts": {"mTimestamp": {"mtime": 1753523650008.387, "uuid": "4fec5f67-8da2-438f-9ffc-6732e3de3cf0"}, "chunkId": "34551532144943d5b236508c5ff8c0f8cd4ba4f3", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 39}, "end": {"line": 1, "column": 43}}}, {"value": "../base/System", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 39}}}, {"value": "../base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts": {"mTimestamp": {"mtime": 1753527484600.681, "uuid": "2564d02b-7111-4a64-aa28-de874242b1f0"}, "chunkId": "eb065a22717f447f6d2a5623f6f7fdb1b64865a1", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts": {"mTimestamp": {"mtime": 1753523940083.7605, "uuid": "21904ac5-11f3-4245-b818-c752c4f8ce8f"}, "chunkId": "e486c6d3a19894b9b42ded541c8d00f19e609286", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/gizmos/Emitter.ts": {"mTimestamp": {"mtime": 1753524596363.933, "uuid": "b94b54e2-e54c-4c15-9235-2115d8ba8651"}, "chunkId": "929763e07631558fcf7acf42a592befb34df2638", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 32}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/gizmos/EmitterGizmo.ts": {"mTimestamp": {"mtime": 1753527797374.6707, "uuid": "92b822be-e838-4774-8286-851dc74ce5af"}, "chunkId": "b37c1a13f611ddf8b31757285f1055a41cc2342e", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 7, "column": 55}, "end": {"line": 7, "column": 59}}}, {"value": "cc/env", "loc": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Data/Bag.ts": {"mTimestamp": {"mtime": 1754103639659.6096, "uuid": "3b4f9fad-1090-4fff-a3ee-59d5236afae9"}, "chunkId": "44234b212c7f7028f9f8da6892a6ad94537d13ba", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Data/BaseInfo.ts": {"mTimestamp": {"mtime": 1754103639660.6306, "uuid": "c10d4e6d-42cf-4f38-b668-7808d5842447"}, "chunkId": "5f01edefedae50f5def503929e8dabf4100f54b2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Data/DataManager.ts": {"mTimestamp": {"mtime": 1754103639661.6365, "uuid": "5eca8651-1e64-4942-b097-92772b508c85"}, "chunkId": "07309fdf17a16dd1f1adf126f0d90982627a2d65", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "./Bag", "resolved": "__unresolved_1", "loc": {"start": {"line": 1, "column": 20}, "end": {"line": 1, "column": 27}}}, {"value": "./BaseInfo", "resolved": "__unresolved_2", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 37}}}, {"value": "./GameLevel", "resolved": "__unresolved_3", "loc": {"start": {"line": 3, "column": 26}, "end": {"line": 3, "column": 39}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Data/Bag.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Data/BaseInfo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Data/GameLevel.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Data/GameLevel.ts": {"mTimestamp": {"mtime": 1754103639662.662, "uuid": "07d95d45-a5c8-4ecb-a0d3-bb5f065883dc"}, "chunkId": "724ec8cc92ad5dd7afe326ef3618dbfd39429adb", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts": {"mTimestamp": {"mtime": 1754103639578.0283, "uuid": "402f76e8-a281-47a5-8c49-298291c49c53"}, "chunkId": "e3d3e245530e7d841c90325c4b8091e9d57ec02c", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 92}, "end": {"line": 1, "column": 96}}}, {"value": "./factroy/AnimFactory", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 51}}}, {"value": "./factroy/EnemyBulletFactory", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 35}, "end": {"line": 3, "column": 65}}}, {"value": "./factroy/EnemyFactory", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 53}}}, {"value": "./factroy/GoodsFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 53}}}, {"value": "./factroy/PlayerBulletFactory", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 36}, "end": {"line": 7, "column": 67}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts": {"mTimestamp": {"mtime": 1754108041348.2964, "uuid": "3c748240-8509-4d0f-9045-5d2db6ddd6a3"}, "chunkId": "52f1849c6d3df0e45c5028b3d0b7fdfbe73a5505", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 56}, "end": {"line": 1, "column": 60}}}, {"value": "./GizmoDrawer", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 49}, "end": {"line": 2, "column": 64}}}, {"value": "./GizmoUtils", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 41}}}, {"value": "../world/weapon/EmitterArc", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 55}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/EmitterArc.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts": {"mTimestamp": {"mtime": 1754108059732.55, "uuid": "0088d2f5-c793-493c-834c-205c9a50911f"}, "chunkId": "14575ba2e85edcf9562818cfbe6f354b7e2052ea", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 67}, "end": {"line": 1, "column": 71}}}, {"value": "./GizmoUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoUtils.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts": {"mTimestamp": {"mtime": 1754103639618.5972, "uuid": "35b7ed22-0671-47b6-aaaf-01dd52bab33b"}, "chunkId": "3da396b2f75dcf9e8155dd890a93ba6631a39160", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 67}, "end": {"line": 1, "column": 71}}}, {"value": "cc/env", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 31}}}, {"value": "./GizmoDrawer", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 54}, "end": {"line": 3, "column": 69}}}, {"value": "./EmitterArcGizmo", "resolved": "__unresolved_2", "loc": {"start": {"line": 6, "column": 7}, "end": {"line": 6, "column": 26}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoUtils.ts": {"mTimestamp": {"mtime": 1754103639619.8308, "uuid": "cef3dbcf-a904-492a-9b21-a955b1cb2f90"}, "chunkId": "add8b650e171a9bb048c3ca99d83ab3c025d9df1", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 56}, "end": {"line": 1, "column": 60}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/index.ts": {"mTimestamp": {"mtime": 1754103639620.874, "uuid": "29b38698-913f-4357-9a8a-dde8b3838535"}, "chunkId": "a16b7e3cedd4685e1b35564abfc7d3ba640ae503", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "./GizmoDrawer", "resolved": "__unresolved_1", "loc": {"start": {"line": 9, "column": 102}, "end": {"line": 9, "column": 117}}}, {"value": "./GizmoManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": 45}}}, {"value": "./GizmoUtils", "resolved": "__unresolved_3", "loc": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 41}}}, {"value": "./EmitterArcGizmo", "resolved": "__unresolved_4", "loc": {"start": {"line": 14, "column": 32}, "end": {"line": 14, "column": 51}}}, {"value": "./GizmoManager", "resolved": "__unresolved_5", "loc": {"start": {"line": 17, "column": 29}, "end": {"line": 17, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/base/Object.ts": {"mTimestamp": {"mtime": 1754103639259.6196, "uuid": "53e332a8-652b-470a-b1f6-9af2a83b25dd"}, "chunkId": "4834e68a45992cfc1301b934f52cd93d56b57cea", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 38}, "end": {"line": 1, "column": 42}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Bullet.ts": {"mTimestamp": {"mtime": 1754103639508.5166, "uuid": "21904ac5-11f3-4245-b818-c752c4f8ce8f"}, "chunkId": "243d71b6e9c4acffbdaac6583f71982b720f1626", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}, {"value": "../base/Object", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 40}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/Object.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/BulletSystem.ts": {"mTimestamp": {"mtime": 1754103639509.5205, "uuid": "7cb83a17-5595-4c04-9619-63b94934fc55"}, "chunkId": "40ea252cf2e0207c46b7a059621083c015327bb1", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 39}, "end": {"line": 1, "column": 43}}}, {"value": "../base/System", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 39}}}, {"value": "../base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Emitter.ts": {"mTimestamp": {"mtime": 1754103639511.537, "uuid": "2564d02b-7111-4a64-aa28-de874242b1f0"}, "chunkId": "bbfb1f410c36b2f20792898ca75909b49768f34f", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 41}, "end": {"line": 1, "column": 45}}}, {"value": "./Weapon", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Weapon.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/EmitterArc.ts": {"mTimestamp": {"mtime": 1754103639512.561, "uuid": "93e57e61-bb20-4f4f-a6ce-46d73aa1b41b"}, "chunkId": "efa6004f1379a8f353912e634039d3029e8718b4", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "./Emitter", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 35}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Emitter.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Weapon.ts": {"mTimestamp": {"mtime": 1754103639513.5847, "uuid": "e5b0e74a-b97c-4ecd-a431-1d653dbabef7"}, "chunkId": "8284bd7bd5259d3715182180d8a8280b04e0f7d0", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 41}, "end": {"line": 1, "column": 45}}}, {"value": "../base/Object", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 40}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/Object.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/WeaponSlot.ts": {"mTimestamp": {"mtime": 1754103639514.588, "uuid": "85aa9f30-7a4e-4be8-bf9b-5ccefb0db394"}, "chunkId": "4780daec0ed224fc902524158ba8bba1c96a2bed", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "../base/Object", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 40}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/Object.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Network/DevLoginData.ts": {"mTimestamp": {"mtime": 1754103639678.7832, "uuid": "b55fbad0-f4d4-4d77-8b4c-2fc93b95395d"}, "chunkId": "c949282bfb846eb6ce3427235fce4909eca81168", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 32}, "end": {"line": 1, "column": 36}}}, {"value": "../Utils/Logger", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 42}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts": {"mTimestamp": {"mtime": 1754103639688.5168, "uuid": "a4f64a5d-e539-44af-afcd-fedea4e5346c"}, "chunkId": "d109cf2e924793efbdac781889e60a40824eca66", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 75}, "end": {"line": 1, "column": 79}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts": {"mTimestamp": {"mtime": 1754103639689.5698, "uuid": "4ac21e28-684f-4d78-be2f-72b4291faa01"}, "chunkId": "5b83959ddb87d5997d87b77a054e9e74646c06fc", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 94}, "end": {"line": 1, "column": 98}}}, {"value": "../IMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 30}}}, {"value": "../GameInstance", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 46}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/IMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/GameInstance.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/ButtonPlus.ts": {"mTimestamp": {"mtime": 1754103639698.0498, "uuid": "13f7f397-2c71-4b2b-a2d7-9b0026823347"}, "chunkId": "67616d3b5c2d38562e0a5432d6e50d8c15bf0d5c", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 61}, "end": {"line": 1, "column": 65}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/DragButton.ts": {"mTimestamp": {"mtime": 1754103639699.1787, "uuid": "24abeccb-3392-4d3d-871c-b3710901a3e7"}, "chunkId": "a9550beaa6a5237c3656d6fa2b93593fe9e802aa", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 96}, "end": {"line": 1, "column": 100}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/List/List.ts": {"mTimestamp": {"mtime": 1754103639710.5017, "uuid": "1f72040c-6625-4564-86fa-921bf1edfdbb"}, "chunkId": "847dcd4ee59d9ec794771a73f82c0ca50f0c169d", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 8, "column": 208}, "end": {"line": 8, "column": 212}}}, {"value": "cc/env", "loc": {"start": {"line": 9, "column": 20}, "end": {"line": 9, "column": 28}}}, {"value": "./ListItem", "resolved": "__unresolved_1", "loc": {"start": {"line": 10, "column": 21}, "end": {"line": 10, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/List/ListItem.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/List/ListItem.ts": {"mTimestamp": {"mtime": 1754103639711.5056, "uuid": "b8d5dd93-f9b0-4836-8cf5-3499a2d76520"}, "chunkId": "1f10150dc285427a2d39750fca8aa21c21c944f7", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 10, "column": 126}, "end": {"line": 10, "column": 130}}}, {"value": "cc/env", "loc": {"start": {"line": 11, "column": 20}, "end": {"line": 11, "column": 28}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/SelectList/uiSelect.ts": {"mTimestamp": {"mtime": 1754103639719.8347, "uuid": "8ccaa0df-7756-4e58-8729-690b090da642"}, "chunkId": "9fd190ac9e0dac9cfc7f9a8b6aa916c710767ad7", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 120}, "end": {"line": 1, "column": 124}}}, {"value": "./uiSelectItem", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/SelectList/uiSelectItem.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/SelectList/uiSelectItem.ts": {"mTimestamp": {"mtime": 1754103639720.835, "uuid": "758bee86-bdae-470f-ae12-79ca68b8bc8d"}, "chunkId": "27acbc0428800084cee152833d4ae0b4d9b53b6f", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 51}, "end": {"line": 1, "column": 55}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/DevLoginUI.ts": {"mTimestamp": {"mtime": 1754103639767.884, "uuid": "47d87031-20f5-4a66-b583-efe12b0f356d"}, "chunkId": "43c368921504c21b8748375c1cf9a52376b3b595", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 84}, "end": {"line": 1, "column": 88}}}, {"value": "../Network/DevLoginData", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 54}}}, {"value": "../GameInstance", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 46}}}, {"value": "../AutoGen/PB/cs_proto.js", "resolved": "__unresolved_3", "loc": {"start": {"line": 6, "column": 20}, "end": {"line": 6, "column": 47}}}, {"value": "../UI/Components/Common/SelectList/uiSelect", "resolved": "__unresolved_4", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 70}}}, {"value": "../Utils/Logger", "resolved": "__unresolved_5", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 42}}}, {"value": "../UI/UIMgr", "resolved": "__unresolved_6", "loc": {"start": {"line": 9, "column": 39}, "end": {"line": 9, "column": 52}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Network/DevLoginData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/GameInstance.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.mjs?cjs=&original=.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/SelectList/uiSelect.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/LoadingUI.ts": {"mTimestamp": {"mtime": 1754103639768.8955, "uuid": "e4d84293-318b-4ae9-b004-f76e257b282e"}, "chunkId": "5ad20fa2041093b792895e11795119f711e1a371", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 54}, "end": {"line": 1, "column": 58}}}, {"value": "./UIMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 32}, "end": {"line": 2, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Main/BattleUI.ts": {"mTimestamp": {"mtime": 1754103639769.8982, "uuid": "a1914169-4cba-4c8c-bb8c-f63021adaa0d"}, "chunkId": "1558f1928a5c90e3f9d573f4ee0cc39c56541d15", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 37}, "end": {"line": 1, "column": 41}}}, {"value": "../Components/Common/Button/ButtonPlus", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 67}}}, {"value": "../LoadingUI", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 26}, "end": {"line": 3, "column": 40}}}, {"value": "../UIMgr", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 39}, "end": {"line": 4, "column": 49}}}, {"value": "./BottomUI", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 37}}}, {"value": "./PlaneUI", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 35}}}, {"value": "./ShopUI", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 33}}}, {"value": "./TalentUI", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 37}}}, {"value": "./TopUI", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/ButtonPlus.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/LoadingUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/BottomUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/PlaneUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/ShopUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/TalentUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/TopUI.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Main/BottomUI.ts": {"mTimestamp": {"mtime": 1754103639770.5847, "uuid": "8ae4ef60-5a85-408a-9490-cbaff5fce3b5"}, "chunkId": "2549a43ffd88c9b6db3b5ecb7ca74f1814dc3696", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 77}, "end": {"line": 1, "column": 81}}}, {"value": "../Components/Common/Button/ButtonPlus", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 67}}}, {"value": "../UIMgr", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 39}, "end": {"line": 3, "column": 49}}}, {"value": "./BattleUI", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 37}}}, {"value": "./PlaneUI", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 35}}}, {"value": "./ShopUI", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 33}}}, {"value": "./SkyIslandUI", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 43}}}, {"value": "./TalentUI", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 37}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/ButtonPlus.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/BattleUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/PlaneUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/ShopUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/SkyIslandUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Main/TalentUI.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Main/PlaneUI.ts": {"mTimestamp": {"mtime": 1754103639773.625, "uuid": "139d8d34-fb41-421a-8ce7-cb82ee1be7c0"}, "chunkId": "8843894e63e825072e318536d719202e89bdc5db", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "../Components/Common/Button/ButtonPlus", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 67}}}, {"value": "../UIMgr", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": 42}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/ButtonPlus.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Main/ShopUI.ts": {"mTimestamp": {"mtime": 1754103639776.708, "uuid": "1c828fc9-0ffb-4d20-9511-4939bbb9f0f6"}, "chunkId": "473167950d9f3106ba8633b707c03416215efb35", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "../Components/Common/Button/ButtonPlus", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 67}}}, {"value": "../UIMgr", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": 42}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/ButtonPlus.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Main/SkyIslandUI.ts": {"mTimestamp": {"mtime": 1754103639777.7122, "uuid": "ca81f721-e8fb-40cf-8ca5-cd601d367732"}, "chunkId": "f51644fa2e78f45332fba684d7cb3f2bd5d77150", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "../Components/Common/Button/ButtonPlus", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 67}}}, {"value": "../UIMgr", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": 42}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/ButtonPlus.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Main/TalentUI.ts": {"mTimestamp": {"mtime": 1754103639778.7666, "uuid": "1d489ed0-c902-4020-a1d9-7490309365b2"}, "chunkId": "3fdd1d2f52d76aa47f18ca8091baf507e5b1172a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "../Components/Common/Button/ButtonPlus", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 67}}}, {"value": "../UIMgr", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": 42}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/ButtonPlus.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/Main/TopUI.ts": {"mTimestamp": {"mtime": 1754103639779.9001, "uuid": "4886f711-68a8-408f-884f-15dc89f8ee93"}, "chunkId": "2d711da9452c077757cea9bcc6057e462a557fd5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "../UIMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 32}, "end": {"line": 2, "column": 42}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts": {"mTimestamp": {"mtime": 1754103639786.9622, "uuid": "5b94c900-84c2-439b-9b7d-0eeaff67a231"}, "chunkId": "c0f2328113ccb54a8eb3b21946c38d5524d23e24", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 3, "column": 125}, "end": {"line": 3, "column": 129}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts": {"mTimestamp": {"mtime": 1754103639794.4622, "uuid": "b2c3d4e5-f6g7-8901-bcde-f23456789012"}, "chunkId": "1267a4e4e87636ca3fcca0ce75a2153737637253", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 63}, "end": {"line": 1, "column": 67}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.mjs?cjs=&original=.js": {"mTimestamp": 1754103639642.5251, "chunkId": "fb3ed27fd25a18b2e4b976c810ad0e98af3f0c92", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "./cs_proto.js", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 51}}}, {"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 19}, "end": {"line": 3, "column": 52}}}, {"value": "./cs_proto.js", "resolved": "__unresolved_3", "loc": {"start": {"line": 8, "column": 14}, "end": {"line": 8, "column": 29}}}, {"value": "./cs_proto.js", "resolved": "__unresolved_4", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 44}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/index.mjs?cjs=&original=.js": {"mTimestamp": 1754103641085.2722, "chunkId": "6d3e68672dcaef837f498fdb680d212abeff87e7", "imports": [{"value": "./index.js", "resolved": "__unresolved_0", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 48}}}, {"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 19}, "end": {"line": 3, "column": 52}}}, {"value": "./index.js", "resolved": "__unresolved_2", "loc": {"start": {"line": 8, "column": 14}, "end": {"line": 8, "column": 26}}}, {"value": "./index.js", "resolved": "__unresolved_3", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/index.js"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/index.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/index.js"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js": {"mTimestamp": {"mtime": 1754103639642.5251, "uuid": "f7ffe2e2-907b-4607-bbee-276c4896e3ec"}, "chunkId": "8c8a29bb6e34a9309c69802d46b90d9d818ef3ec", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "protobufjs/minimal.js", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 59}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js"}, "messages": []}]}, "cce:/internal/ml/cjs-loader.mjs": {"mTimestamp": 1754103703198, "chunkId": "a0d063530de23ddade53e1a935bf64c81277c061", "imports": [], "type": "esm", "resolutions": []}, "file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js": {"mTimestamp": 1753501234081.882, "chunkId": "19c5da1528f9eb904915155f0fb7ecb5349b3fd5", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./src/index-minimal", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 57}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js": {"mTimestamp": 1753501234114.5857, "chunkId": "bd1efc31128ae550054ceba1b548b9f49a0f1aae", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./writer", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 46}}}, {"value": "./writer_buffer", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 54}}}, {"value": "./reader", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 47}}}, {"value": "./reader_buffer", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 54}}}, {"value": "./util/minimal", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": 53}}}, {"value": "./rpc", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 37}, "end": {"line": 7, "column": 44}}}, {"value": "./roots", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 37}, "end": {"line": 8, "column": 46}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/roots.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js": {"mTimestamp": 1753501234147.1794, "chunkId": "f8962a845727f76655d221c94ef7ca6626e23ceb", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./util/minimal", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js": {"mTimestamp": 1753501234148.1787, "chunkId": "9e04bad75f933ba0d84c33840dcdd64dc4d15291", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./writer", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 46}}}, {"value": "./util/minimal", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 53}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js": {"mTimestamp": 1753501234118.5857, "chunkId": "e3d0585cbe8b83afafa8cc7712fab5b65b43b313", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./util/minimal", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js": {"mTimestamp": 1753501234119.5862, "chunkId": "cee298425f64e84764641d1edc9de77392e6a191", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./reader", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 46}}}, {"value": "./util/minimal", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 53}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js": {"mTimestamp": 1753501234137.6353, "chunkId": "3bc827fdf6896d4bdc639f626c3c302b13615ec4", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "@protobufjs/aspromise", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 59}}}, {"value": "@protobufjs/base64", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 57}}}, {"value": "@protobufjs/eventemitter", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 63}}}, {"value": "@protobufjs/float", "loc": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 56}}}, {"value": "@protobufjs/inquire", "loc": {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": 58}}}, {"value": "@protobufjs/utf8", "loc": {"start": {"line": 7, "column": 37}, "end": {"line": 7, "column": 55}}}, {"value": "@protobufjs/pool", "loc": {"start": {"line": 8, "column": 37}, "end": {"line": 8, "column": 55}}}, {"value": "./longbits", "resolved": "__unresolved_1", "loc": {"start": {"line": 9, "column": 37}, "end": {"line": 9, "column": 49}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/@protobufjs/aspromise/index.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/@protobufjs/base64/index.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/@protobufjs/eventemitter/index.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/@protobufjs/float/index.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/@protobufjs/inquire/index.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/@protobufjs/utf8/index.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/@protobufjs/pool/index.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/util/longbits.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc.js": {"mTimestamp": 1753501234133.5857, "chunkId": "21e3b8f0dcc8af32f9d11fb67233e89f73a804bb", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./rpc/service", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 51}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc/service.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/protobufjs/src/roots.js": {"mTimestamp": 1753501234120.5857, "chunkId": "e8f8aba6eb1d0e0856af81c66167476beb5f94f5", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/@protobufjs/aspromise/index.js": {"mTimestamp": 1753501233638.9478, "chunkId": "2dc154f5d73b7c3df490a78b59dce8854bfac296", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/@protobufjs/base64/index.js": {"mTimestamp": 1753501233651.1016, "chunkId": "5877bd7047e42f39647f4b2c7fbe318174fb9e67", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/@protobufjs/eventemitter/index.js": {"mTimestamp": 1753501233684.6147, "chunkId": "029bd0977250dfbb1a941d6af005ac13dd7274f7", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/@protobufjs/float/index.js": {"mTimestamp": 1753501233712.8308, "chunkId": "081e7960460cd6435e3945ce4934845bc70df678", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/@protobufjs/inquire/index.js": {"mTimestamp": 1753501233737.5337, "chunkId": "a9939944624d6fd870b64008f9da297fd860c67b", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/@protobufjs/utf8/index.js": {"mTimestamp": 1753501233782.7969, "chunkId": "de1fe9b319efd692ebb13cb9bfed16ab791f1684", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/@protobufjs/pool/index.js": {"mTimestamp": 1753501233766.94, "chunkId": "21f251e756c75b240f2aa36891fef7a0f10e03b2", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/protobufjs/src/util/longbits.js": {"mTimestamp": 1753501234136.6057, "chunkId": "b32d44179fea9a606d6e21a12da14979ca6d2021", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "../util/minimal", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 53}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc/service.js": {"mTimestamp": 1753501234120.5857, "chunkId": "000ccbb51cdcaedd8f231360cdd126860f871ab2", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "../util/minimal", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 53}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/index.js": {"mTimestamp": 1754103641085.2722, "chunkId": "b64ef3a409e403602bbbe6c159f5c5f661bae745", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./x64-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 49}}}, {"value": "./lib-typedarrays", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 56}}}, {"value": "./enc-utf16", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 50}}}, {"value": "./enc-base64", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": 51}}}, {"value": "./enc-base64url", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 37}, "end": {"line": 7, "column": 54}}}, {"value": "./md5", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 37}, "end": {"line": 8, "column": 44}}}, {"value": "./sha1", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 37}, "end": {"line": 9, "column": 45}}}, {"value": "./sha256", "resolved": "__unresolved_9", "loc": {"start": {"line": 10, "column": 37}, "end": {"line": 10, "column": 47}}}, {"value": "./sha224", "resolved": "__unresolved_10", "loc": {"start": {"line": 11, "column": 37}, "end": {"line": 11, "column": 47}}}, {"value": "./sha512", "resolved": "__unresolved_11", "loc": {"start": {"line": 12, "column": 37}, "end": {"line": 12, "column": 47}}}, {"value": "./sha384", "resolved": "__unresolved_12", "loc": {"start": {"line": 13, "column": 38}, "end": {"line": 13, "column": 48}}}, {"value": "./sha3", "resolved": "__unresolved_13", "loc": {"start": {"line": 14, "column": 38}, "end": {"line": 14, "column": 46}}}, {"value": "./ripemd160", "resolved": "__unresolved_14", "loc": {"start": {"line": 15, "column": 38}, "end": {"line": 15, "column": 51}}}, {"value": "./hmac", "resolved": "__unresolved_15", "loc": {"start": {"line": 16, "column": 38}, "end": {"line": 16, "column": 46}}}, {"value": "./pbkdf2", "resolved": "__unresolved_16", "loc": {"start": {"line": 17, "column": 38}, "end": {"line": 17, "column": 48}}}, {"value": "./evpkdf", "resolved": "__unresolved_17", "loc": {"start": {"line": 18, "column": 38}, "end": {"line": 18, "column": 48}}}, {"value": "./cipher-core", "resolved": "__unresolved_18", "loc": {"start": {"line": 19, "column": 38}, "end": {"line": 19, "column": 53}}}, {"value": "./mode-cfb", "resolved": "__unresolved_19", "loc": {"start": {"line": 20, "column": 38}, "end": {"line": 20, "column": 50}}}, {"value": "./mode-ctr", "resolved": "__unresolved_20", "loc": {"start": {"line": 21, "column": 38}, "end": {"line": 21, "column": 50}}}, {"value": "./mode-ctr-gladman", "resolved": "__unresolved_21", "loc": {"start": {"line": 22, "column": 38}, "end": {"line": 22, "column": 58}}}, {"value": "./mode-ofb", "resolved": "__unresolved_22", "loc": {"start": {"line": 23, "column": 38}, "end": {"line": 23, "column": 50}}}, {"value": "./mode-ecb", "resolved": "__unresolved_23", "loc": {"start": {"line": 24, "column": 38}, "end": {"line": 24, "column": 50}}}, {"value": "./pad-ansix923", "resolved": "__unresolved_24", "loc": {"start": {"line": 25, "column": 38}, "end": {"line": 25, "column": 54}}}, {"value": "./pad-iso10126", "resolved": "__unresolved_25", "loc": {"start": {"line": 26, "column": 38}, "end": {"line": 26, "column": 54}}}, {"value": "./pad-iso97971", "resolved": "__unresolved_26", "loc": {"start": {"line": 27, "column": 38}, "end": {"line": 27, "column": 54}}}, {"value": "./pad-zeropadding", "resolved": "__unresolved_27", "loc": {"start": {"line": 28, "column": 38}, "end": {"line": 28, "column": 57}}}, {"value": "./pad-nopadding", "resolved": "__unresolved_28", "loc": {"start": {"line": 29, "column": 38}, "end": {"line": 29, "column": 55}}}, {"value": "./format-hex", "resolved": "__unresolved_29", "loc": {"start": {"line": 30, "column": 38}, "end": {"line": 30, "column": 52}}}, {"value": "./aes", "resolved": "__unresolved_30", "loc": {"start": {"line": 31, "column": 38}, "end": {"line": 31, "column": 45}}}, {"value": "./tripledes", "resolved": "__unresolved_31", "loc": {"start": {"line": 32, "column": 38}, "end": {"line": 32, "column": 51}}}, {"value": "./rc4", "resolved": "__unresolved_32", "loc": {"start": {"line": 33, "column": 38}, "end": {"line": 33, "column": 45}}}, {"value": "./rabbit", "resolved": "__unresolved_33", "loc": {"start": {"line": 34, "column": 38}, "end": {"line": 34, "column": 48}}}, {"value": "./rabbit-legacy", "resolved": "__unresolved_34", "loc": {"start": {"line": 35, "column": 38}, "end": {"line": 35, "column": 55}}}, {"value": "./blowfish", "resolved": "__unresolved_35", "loc": {"start": {"line": 36, "column": 38}, "end": {"line": 36, "column": 50}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/x64-core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/lib-typedarrays.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/enc-utf16.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/enc-base64.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/enc-base64url.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/md5.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/sha1.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/sha256.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/sha224.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/sha512.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/sha384.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/sha3.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/ripemd160.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/hmac.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/pbkdf2.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/evpkdf.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/mode-cfb.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/mode-ctr.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/mode-ctr-gladman.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/mode-ofb.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/mode-ecb.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/pad-ansix923.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/pad-iso10126.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/pad-iso97971.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/pad-zeropadding.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/pad-nopadding.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/format-hex.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/aes.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/tripledes.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/rc4.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/rabbit.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/rabbit-legacy.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/blowfish.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/core.js": {"mTimestamp": 1754103640998.9678, "chunkId": "e9dc75216d02175f8840f81538fe9d98f28f225e", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "crypto", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "data:text/javascript,%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20export%20const%20__cjsMetaURL%20%3D%20'crypto'%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/x64-core.js": {"mTimestamp": 1754103641102.756, "chunkId": "75b43c14ac639813ff207172c0d864a53b18e996", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/lib-typedarrays.js": {"mTimestamp": 1754103641086.3809, "chunkId": "db8edb686d0f8f814a6d1f3687b4afa681c171dc", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/enc-utf16.js": {"mTimestamp": 1754103641078.4258, "chunkId": "7828890f8710922932e5ab7731a0d05653094571", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/enc-base64.js": {"mTimestamp": 1754103641075.2083, "chunkId": "df020fa691ecdcfa1ab17ffdb0c35179a767fb9a", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/enc-base64url.js": {"mTimestamp": 1754103641076.221, "chunkId": "1f0595ad2ef0a195f498496861d888725b778d2c", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/md5.js": {"mTimestamp": 1754103641086.3809, "chunkId": "7cd07f8b0648a4f2398f916ea4ff88fdcbc09972", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/sha1.js": {"mTimestamp": 1754103641095.623, "chunkId": "2a2b5da3a6b4535f66f493dd8ca796a353498f63", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/sha256.js": {"mTimestamp": 1754103641098.3723, "chunkId": "362111050de5a65aeb8dad4ecfffb96b625ae0b6", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/sha224.js": {"mTimestamp": 1754103641097.0076, "chunkId": "e17453c00f098dd3944850b9ed79060025a9f65a", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./sha256", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 47}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/sha256.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/sha512.js": {"mTimestamp": 1754103641100.6045, "chunkId": "7f9a6a53d00d2e31c653b0fd93a771cd30183718", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./x64-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 49}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/x64-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/sha384.js": {"mTimestamp": 1754103641099.3855, "chunkId": "5286fe9399e518fe7a689362bce32ea5afeb5980", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./x64-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 49}}}, {"value": "./sha512", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 47}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/x64-core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/sha512.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/sha3.js": {"mTimestamp": 1754103641099.3855, "chunkId": "02aba28027c123f0542cc812a0ab707996365f09", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./x64-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 49}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/x64-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/ripemd160.js": {"mTimestamp": 1754103641095.623, "chunkId": "912cde9ad7825428681b617cbcff9953419739b0", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/hmac.js": {"mTimestamp": 1754103641085.2722, "chunkId": "e3a72238868d4696883159324a0771129dd061c3", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/pbkdf2.js": {"mTimestamp": 1754103641093.623, "chunkId": "8c1cee604c44cb35efe2adf1c920eee0e896e107", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./sha256", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 47}}}, {"value": "./hmac", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 45}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/sha256.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/hmac.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/evpkdf.js": {"mTimestamp": 1754103641078.4258, "chunkId": "b37b65c36911fa6fd0d47e8f281747979656b5a0", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./sha1", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 45}}}, {"value": "./hmac", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 45}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/sha1.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/hmac.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js": {"mTimestamp": 1754103640997.9673, "chunkId": "0ec7695a06e6b6c4e640628df2a81505b89c2a7e", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./evpkdf", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 47}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/evpkdf.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/mode-cfb.js": {"mTimestamp": 1754103641087.385, "chunkId": "e0665bf9af35c56d4f7a695a0dba0b3133a3e2b6", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/mode-ctr.js": {"mTimestamp": 1754103641088.4558, "chunkId": "b32576a8fcbb78252d8c65cf59b6e909493f7295", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/mode-ctr-gladman.js": {"mTimestamp": 1754103641087.385, "chunkId": "fdd5fdcf9020e09963104ef9a589015f60563c36", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/mode-ofb.js": {"mTimestamp": 1754103641089.5037, "chunkId": "3b28f25d2c6aadf7c5faed0994f7f7810f323f2c", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/mode-ecb.js": {"mTimestamp": 1754103641088.4558, "chunkId": "b0f2f4b42f38990406443c07dd3760ab2e1c2381", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/pad-ansix923.js": {"mTimestamp": 1754103641090.5166, "chunkId": "572c2482b7bf60bd5824e5322dc45f9c58de9bbe", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/pad-iso10126.js": {"mTimestamp": 1754103641090.5166, "chunkId": "8a4c1aafd9384abb97d7e749583f27b1f3136b6e", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/pad-iso97971.js": {"mTimestamp": 1754103641091.6184, "chunkId": "877588c1388f99a40b5d00413a00be8fa44ae7bb", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/pad-zeropadding.js": {"mTimestamp": 1754103641092.6233, "chunkId": "097ebd72da55e0ef05ea1980f7afdf5f87854762", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/pad-nopadding.js": {"mTimestamp": 1754103641091.6184, "chunkId": "2e2dc482bc3c84f8e05401101816db5cb3b68d53", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/format-hex.js": {"mTimestamp": 1754103641079.7378, "chunkId": "04aad75c26270c74311e3fd782bf005c88be3cf6", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./cipher-core", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/aes.js": {"mTimestamp": 1754103640995.9668, "chunkId": "95bf73f789595b749bd0d225b2a64107f6576b23", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./enc-base64", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 51}}}, {"value": "./md5", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 44}}}, {"value": "./evpkdf", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 47}}}, {"value": "./cipher-core", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/enc-base64.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/md5.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/evpkdf.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/tripledes.js": {"mTimestamp": 1754103641101.7522, "chunkId": "48c9995c8f670e5cde94784af8d1c48b13416c64", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./enc-base64", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 51}}}, {"value": "./md5", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 44}}}, {"value": "./evpkdf", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 47}}}, {"value": "./cipher-core", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/enc-base64.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/md5.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/evpkdf.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/rc4.js": {"mTimestamp": 1754103641094.6233, "chunkId": "eff24ca334ada5a05988240714712a9e486b7e3e", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./enc-base64", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 51}}}, {"value": "./md5", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 44}}}, {"value": "./evpkdf", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 47}}}, {"value": "./cipher-core", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/enc-base64.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/md5.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/evpkdf.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/rabbit.js": {"mTimestamp": 1754103641094.6233, "chunkId": "ae75311657e067b9d569d89ccb3ba9342237187f", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./enc-base64", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 51}}}, {"value": "./md5", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 44}}}, {"value": "./evpkdf", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 47}}}, {"value": "./cipher-core", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/enc-base64.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/md5.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/evpkdf.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/rabbit-legacy.js": {"mTimestamp": 1754103641093.623, "chunkId": "ffa17cd49b85ae30500893647450e3303214f419", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./enc-base64", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 51}}}, {"value": "./md5", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 44}}}, {"value": "./evpkdf", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 47}}}, {"value": "./cipher-core", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/enc-base64.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/md5.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/evpkdf.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "file:///E:/M2Game/Client/node_modules/crypto-js/blowfish.js": {"mTimestamp": 1754103640996.967, "chunkId": "34768833a2d2a9f8362638e4f3f8a0f752c57b2d", "imports": [{"value": "cce:/internal/ml/cjs-loader.mjs", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 56}}}, {"value": "./core", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 2, "column": 44}}}, {"value": "./enc-base64", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 51}}}, {"value": "./md5", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 44}}}, {"value": "./evpkdf", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 47}}}, {"value": "./cipher-core", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": 52}}}], "type": "commonjs", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/ml/cjs-loader.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/core.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/enc-base64.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/md5.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/evpkdf.js"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/node_modules/crypto-js/cipher-core.js"}, "messages": []}]}, "data:text/javascript,%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20export%20const%20__cjsMetaURL%20%3D%20'crypto'%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20": {"mTimestamp": 0, "chunkId": "f094a9b11f9e717b0d194869cd74602637adffee", "imports": [], "type": "esm", "resolutions": []}, "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/LevelBaker.ts": {"mTimestamp": {"mtime": 1754104280536.666, "uuid": "3744e2d4-2826-49ac-9051-13b40bc3e1a0"}, "chunkId": "6f784e75fc9a213b0af0102f480ade0b6819fc78", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PathBaker.ts": {"mTimestamp": {"mtime": 1754109329828.127, "uuid": "3ffb0bb6-1f48-4fca-95e0-436f67f97626"}, "chunkId": "da3a5aac5f0797562e64a16aa877d1f5f0f5ef64", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 55}, "end": {"line": 1, "column": 59}}}, {"value": "../Data/PathPoint", "resolved": "__unresolved_1", "loc": {"start": {"line": 4, "column": 26}, "end": {"line": 4, "column": 45}}}, {"value": "./PointBaker", "resolved": "__unresolved_2", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/Baker.ts": {"mTimestamp": {"mtime": 1754104893829.228, "uuid": "80f78c47-5113-4ca3-8d91-ed3fc8bf05c7"}, "chunkId": "702a66a0669a6b7f347a886225c4f9b9fc19b649", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PathGizmo.ts": {"mTimestamp": {"mtime": 1754109593520.1504, "uuid": "e52da092-c15d-465d-8c87-9cc7b9a207df"}, "chunkId": "097ff54b16ee4025b9dc2a378ad676cdbf3c4f08", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "./GizmoDrawer", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 49}, "end": {"line": 2, "column": 64}}}, {"value": "../world/level/Baker/PathBaker", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 26}, "end": {"line": 4, "column": 58}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PathBaker.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PointGizmo.ts": {"mTimestamp": {"mtime": 1754109492585.9358, "uuid": "120532e2-9191-45f8-a497-1e35cf4a9b2f"}, "chunkId": "6e74d118ea5583f4082195e41ebc872cf562d924", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 56}, "end": {"line": 2, "column": 60}}}, {"value": "./GizmoDrawer", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 49}, "end": {"line": 3, "column": 64}}}, {"value": "../world/level/Baker/PointBaker", "resolved": "__unresolved_2", "loc": {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": 60}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts": {"mTimestamp": {"mtime": 1754109322436.2476, "uuid": "a11f8059-a8cf-44b1-bdeb-8375267efd64"}, "chunkId": "6513f30e3bfe3f5570d9ea7e8013aa26d4ef44a7", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 44}, "end": {"line": 2, "column": 48}}}, {"value": "../Data/PathPoint", "resolved": "__unresolved_1", "loc": {"start": {"line": 5, "column": 26}, "end": {"line": 5, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts": {"mTimestamp": {"mtime": 1754109312679.3188, "uuid": "77e8d921-873c-468d-8194-35d4dfd22809"}, "chunkId": "295f944ba5e787926e9e3cd797c68a5e1b32ccfc", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 44}, "end": {"line": 2, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}}}