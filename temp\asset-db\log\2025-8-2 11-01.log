2025-8-2 11:01:46-log: Cannot access game frame or container.
2025-8-2 11:01:47-debug: asset-db:require-engine-code (628ms)
2025-8-2 11:01:47-log: [box2d]:box2d wasm lib loaded.
2025-8-2 11:01:47-log: meshopt wasm decoder initialized
2025-8-2 11:01:47-log: [bullet]:bullet wasm lib loaded.
2025-8-2 11:01:47-log: Cocos Creator v3.8.6
2025-8-2 11:01:47-log: Using legacy pipeline
2025-8-2 11:01:47-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.99MB, end 80.12MB, increase: 49.13MB
2025-8-2 11:01:47-log: Forward render pipeline initialized.
2025-8-2 11:01:47-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.02MB, end 84.07MB, increase: 3.05MB
2025-8-2 11:01:48-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.87MB, end 228.68MB, increase: 147.81MB
2025-8-2 11:01:48-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.14MB, end 228.71MB, increase: 148.57MB
2025-8-2 11:01:48-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.24MB, end 228.47MB, increase: 3.23MB
2025-8-2 11:01:48-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.11MB, end 224.99MB, increase: 140.89MB
2025-8-2 11:01:48-debug: run package(runtime-dev-tools) handler(enable) success!
2025-8-2 11:01:48-debug: run package(runtime-dev-tools) handler(enable) start
2025-8-2 11:01:48-debug: run package(taobao-mini-game) handler(enable) start
2025-8-2 11:01:48-debug: run package(taobao-mini-game) handler(enable) success!
2025-8-2 11:01:48-debug: run package(vivo-mini-game) handler(enable) success!
2025-8-2 11:01:48-debug: run package(web-desktop) handler(enable) start
2025-8-2 11:01:48-debug: run package(vivo-mini-game) handler(enable) start
2025-8-2 11:01:48-debug: run package(web-desktop) handler(enable) success!
2025-8-2 11:01:48-debug: run package(web-mobile) handler(enable) start
2025-8-2 11:01:48-debug: run package(web-mobile) handler(enable) success!
2025-8-2 11:01:48-debug: run package(wechatgame) handler(enable) success!
2025-8-2 11:01:48-debug: run package(wechatgame) handler(enable) start
2025-8-2 11:01:48-debug: run package(wechatprogram) handler(enable) start
2025-8-2 11:01:48-debug: run package(windows) handler(enable) start
2025-8-2 11:01:48-debug: run package(wechatprogram) handler(enable) success!
2025-8-2 11:01:48-debug: run package(xiaomi-quick-game) handler(enable) start
2025-8-2 11:01:48-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-8-2 11:01:48-debug: run package(im-plugin) handler(enable) start
2025-8-2 11:01:48-debug: run package(windows) handler(enable) success!
2025-8-2 11:01:48-debug: run package(cocos-service) handler(enable) start
2025-8-2 11:01:48-debug: run package(cocos-service) handler(enable) success!
2025-8-2 11:01:48-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-8-2 11:01:48-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-8-2 11:01:48-debug: run package(im-plugin) handler(enable) success!
2025-8-2 11:01:48-debug: run package(level-editor-extension) handler(enable) start
2025-8-2 11:01:48-debug: run package(level-editor-extension) handler(enable) success!
2025-8-2 11:01:48-debug: run package(localization-editor) handler(enable) success!
2025-8-2 11:01:48-debug: run package(placeholder) handler(enable) start
2025-8-2 11:01:48-debug: run package(localization-editor) handler(enable) start
2025-8-2 11:01:48-debug: run package(placeholder) handler(enable) success!
2025-8-2 11:01:48-debug: asset-db:worker-init: initPlugin (1499ms)
2025-8-2 11:01:48-debug: Run asset db hook programming:beforePreStart ...
2025-8-2 11:01:48-debug: [Assets Memory track]: asset-db:worker-init start:30.98MB, end 225.78MB, increase: 194.80MB
2025-8-2 11:01:48-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-2 11:01:48-debug: Run asset db hook programming:beforePreStart success!
2025-8-2 11:01:48-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-2 11:01:48-debug: asset-db:worker-init (2437ms)
2025-8-2 11:01:48-debug: asset-db-hook-programming-beforePreStart (167ms)
2025-8-2 11:01:48-debug: asset-db-hook-engine-extends-beforePreStart (167ms)
2025-8-2 11:01:48-debug: Preimport db internal success
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\resources
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Luban
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Network
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Utils
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\Main
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\AutoGen\PB
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\factroy
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Main
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\prefab\ui\ResUpdate
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\Main\Compents
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components\Common
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\weapon
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components\Common\List
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components\Common\SelectList
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components\Common\Button
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\MainUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\GameInstance.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\BaseInfo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\Bag.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\DataManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\GameLevel.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\Anim.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\Enemy.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\EnemyBullet.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\GamePersistNode.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\Goods.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\MainGame.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\Player.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\PlayerBullet.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Network\DevLoginData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Network\NetMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate\audioManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate\ResUpdate.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate\RootPersist.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\DevLoginUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\LoadingUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\UIMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Utils\Logger.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\AutoGen\PB\cs_proto.d.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\factroy\AnimFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\factroy\EnemyFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\factroy\EnemyBulletFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\factroy\GameFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\factroy\GoodsFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\factroy\PlayerBulletFactory.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\GizmoManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\GizmoUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\index.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\Bootstrap.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\index.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Main\BattleUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Main\BottomUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Main\PlaneUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Main\SkyIslandUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Main\ShopUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Main\TalentUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Main\TopUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base\Object.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base\System.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base\SystemContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base\TypeID.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base\World.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\weapon\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\weapon\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\weapon\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\weapon\EmitterArc.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\weapon\WeaponSlot.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components\Common\Button\DragButton.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components\Common\Button\ButtonPlus.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components\Common\List\List.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components\Common\List\ListItem.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components\Common\SelectList\uiSelect.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\UI\Components\Common\SelectList\uiSelectItem.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:01:49-debug: Preimport db assets success
2025-8-2 11:01:49-debug: Run asset db hook programming:afterPreStart ...
2025-8-2 11:01:49-debug: starting packer-driver...
2025-8-2 11:02:03-debug: initialize scripting environment...
2025-8-2 11:02:03-debug: [[Executor]] prepare before lock
2025-8-2 11:02:03-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-2 11:02:03-debug: [[Executor]] prepare after unlock
2025-8-2 11:02:03-debug: Run asset db hook programming:afterPreStart success!
2025-8-2 11:02:03-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-2 11:02:03-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-2 11:02:03-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.79MB, end 232.12MB, increase: 6.32MB
2025-8-2 11:02:03-debug: Start up the 'internal' database...
2025-8-2 11:02:04-debug: asset-db:worker-effect-data-processing (326ms)
2025-8-2 11:02:04-debug: asset-db-hook-programming-afterPreStart (15262ms)
2025-8-2 11:02:04-debug: asset-db-hook-engine-extends-afterPreStart (327ms)
2025-8-2 11:02:04-debug: Start up the 'assets' database...
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\scenes\DevLogin.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: asset-db:worker-startup-database[internal] (15638ms)
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Main.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\scenes\ResUpdate.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\DevLoginUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\LoadingUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\Main\BottomUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\Main\BattleUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\Main\PlaneUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\Main\ShopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\Main\SkyIslandUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\Main\TalentUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\UI\Main\TopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\scripts\AutoGen\PB\cs_proto.js
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\prefab\ui\ResUpdate\DevSelectServer.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:02:04-debug: lazy register asset handler *
2025-8-2 11:02:04-debug: lazy register asset handler directory
2025-8-2 11:02:04-debug: lazy register asset handler json
2025-8-2 11:02:04-debug: lazy register asset handler text
2025-8-2 11:02:04-debug: lazy register asset handler spine-data
2025-8-2 11:02:04-debug: lazy register asset handler dragonbones
2025-8-2 11:02:04-debug: lazy register asset handler dragonbones-atlas
2025-8-2 11:02:04-debug: lazy register asset handler javascript
2025-8-2 11:02:04-debug: lazy register asset handler typescript
2025-8-2 11:02:04-debug: lazy register asset handler terrain
2025-8-2 11:02:04-debug: lazy register asset handler prefab
2025-8-2 11:02:04-debug: lazy register asset handler scene
2025-8-2 11:02:04-debug: lazy register asset handler tiled-map
2025-8-2 11:02:04-debug: lazy register asset handler sprite-frame
2025-8-2 11:02:04-debug: lazy register asset handler buffer
2025-8-2 11:02:04-debug: lazy register asset handler sign-image
2025-8-2 11:02:04-debug: lazy register asset handler alpha-image
2025-8-2 11:02:04-debug: lazy register asset handler image
2025-8-2 11:02:04-debug: lazy register asset handler texture-cube
2025-8-2 11:02:04-debug: lazy register asset handler erp-texture-cube
2025-8-2 11:02:04-debug: lazy register asset handler texture
2025-8-2 11:02:04-debug: lazy register asset handler render-texture
2025-8-2 11:02:04-debug: lazy register asset handler texture-cube-face
2025-8-2 11:02:04-debug: lazy register asset handler rt-sprite-frame
2025-8-2 11:02:04-debug: lazy register asset handler gltf-mesh
2025-8-2 11:02:04-debug: lazy register asset handler gltf-animation
2025-8-2 11:02:04-debug: lazy register asset handler gltf
2025-8-2 11:02:04-debug: lazy register asset handler gltf-material
2025-8-2 11:02:04-debug: lazy register asset handler gltf-scene
2025-8-2 11:02:04-debug: lazy register asset handler gltf-skeleton
2025-8-2 11:02:04-debug: lazy register asset handler material
2025-8-2 11:02:04-debug: lazy register asset handler gltf-embeded-image
2025-8-2 11:02:04-debug: lazy register asset handler fbx
2025-8-2 11:02:04-debug: lazy register asset handler physics-material
2025-8-2 11:02:04-debug: lazy register asset handler effect
2025-8-2 11:02:04-debug: lazy register asset handler effect-header
2025-8-2 11:02:04-debug: lazy register asset handler animation-clip
2025-8-2 11:02:04-debug: lazy register asset handler animation-graph
2025-8-2 11:02:04-debug: lazy register asset handler audio-clip
2025-8-2 11:02:04-debug: lazy register asset handler animation-graph-variant
2025-8-2 11:02:04-debug: lazy register asset handler ttf-font
2025-8-2 11:02:04-debug: lazy register asset handler particle
2025-8-2 11:02:04-debug: lazy register asset handler bitmap-font
2025-8-2 11:02:04-debug: lazy register asset handler animation-mask
2025-8-2 11:02:04-debug: lazy register asset handler sprite-atlas
2025-8-2 11:02:04-debug: lazy register asset handler label-atlas
2025-8-2 11:02:04-debug: lazy register asset handler render-pipeline
2025-8-2 11:02:04-debug: lazy register asset handler auto-atlas
2025-8-2 11:02:04-debug: lazy register asset handler render-flow
2025-8-2 11:02:04-debug: lazy register asset handler instantiation-material
2025-8-2 11:02:04-debug: lazy register asset handler render-stage
2025-8-2 11:02:04-debug: lazy register asset handler instantiation-animation
2025-8-2 11:02:04-debug: lazy register asset handler instantiation-skeleton
2025-8-2 11:02:04-debug: lazy register asset handler instantiation-mesh
2025-8-2 11:02:04-debug: lazy register asset handler video-clip
2025-8-2 11:02:04-debug: asset-db:worker-startup-database[assets] (15490ms)
2025-8-2 11:02:04-debug: asset-db:start-database (15708ms)
2025-8-2 11:02:04-debug: asset-db:ready (20814ms)
2025-8-2 11:02:04-debug: fix the bug of updateDefaultUserData
2025-8-2 11:02:04-debug: init worker message success
2025-8-2 11:02:04-debug: programming:execute-script (3ms)
2025-8-2 11:02:04-debug: [Build Memory track]: builder:worker-init start:189.49MB, end 208.50MB, increase: 19.01MB
2025-8-2 11:02:04-debug: builder:worker-init (403ms)
2025-8-2 11:06:01-debug: refresh db internal success
2025-8-2 11:06:01-debug: refresh db assets success
2025-8-2 11:06:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 11:06:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 11:06:01-debug: asset-db:refresh-all-database (113ms)
2025-8-2 11:06:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 11:06:42-debug: start refresh asset from E:\M2Game\Client\assets\scripts\Game\world\level\Baker...
2025-8-2 11:06:42-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:06:42-debug: refresh asset E:\M2Game\Client\assets\scripts\Game\world\level success
2025-8-2 11:06:42-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:06:49-debug: refresh db internal success
2025-8-2 11:06:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 11:06:49-debug: refresh db assets success
2025-8-2 11:06:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 11:06:49-debug: asset-db:refresh-all-database (91ms)
2025-8-2 11:06:49-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-2 11:06:49-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-2 11:11:08-debug: refresh db internal success
2025-8-2 11:11:08-debug: refresh db assets success
2025-8-2 11:11:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 11:11:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 11:11:08-debug: asset-db:refresh-all-database (110ms)
2025-8-2 11:11:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 11:11:20-debug: start refresh asset from E:\M2Game\Client\assets\scripts\Game\world\level\Baker\LevelBaker.ts...
2025-8-2 11:11:20-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\LevelBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:11:20-debug: refresh asset E:\M2Game\Client\assets\scripts\Game\world\level\Baker success
2025-8-2 11:11:20-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:11:34-debug: start refresh asset from E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts...
2025-8-2 11:11:34-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:11:34-debug: refresh asset E:\M2Game\Client\assets\scripts\Game\world\level\Baker success
2025-8-2 11:11:34-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:11:44-debug: refresh db internal success
2025-8-2 11:11:44-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:11:44-debug: refresh db assets success
2025-8-2 11:11:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 11:11:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 11:11:44-debug: asset-db:refresh-all-database (113ms)
2025-8-2 11:11:44-debug: asset-db:worker-effect-data-processing (4ms)
2025-8-2 11:11:44-debug: asset-db-hook-engine-extends-afterRefresh (5ms)
2025-8-2 11:11:59-debug: start refresh asset from E:\M2Game\Client\assets\scripts\Game\world\level\Baker\Baker.ts...
2025-8-2 11:11:59-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\Baker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:11:59-debug: refresh asset E:\M2Game\Client\assets\scripts\Game\world\level\Baker success
2025-8-2 11:11:59-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:19:26-debug: refresh db internal success
2025-8-2 11:19:26-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\Baker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:19:26-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:19:26-debug: refresh db assets success
2025-8-2 11:19:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 11:19:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 11:19:26-debug: asset-db:refresh-all-database (71ms)
2025-8-2 11:19:26-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-2 11:19:26-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-2 11:19:27-debug: refresh db internal success
2025-8-2 11:19:27-debug: refresh db assets success
2025-8-2 11:19:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 11:19:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 11:19:27-debug: asset-db:refresh-all-database (59ms)
2025-8-2 11:19:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 11:24:40-debug: refresh db internal success
2025-8-2 11:24:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\Baker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:24:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 11:24:40-debug: refresh db assets success
2025-8-2 11:24:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 11:24:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 11:24:40-debug: asset-db:refresh-all-database (67ms)
2025-8-2 11:36:02-debug: refresh db internal success
2025-8-2 11:36:02-debug: refresh db assets success
2025-8-2 11:36:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 11:36:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 11:36:02-debug: asset-db:refresh-all-database (62ms)
2025-8-2 11:36:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:02:06-debug: refresh db internal success
2025-8-2 12:02:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:02:06-debug: refresh db assets success
2025-8-2 12:02:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:02:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:02:06-debug: asset-db:refresh-all-database (60ms)
2025-8-2 12:02:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:02:07-debug: refresh db internal success
2025-8-2 12:02:07-debug: refresh db assets success
2025-8-2 12:02:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:02:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:02:07-debug: asset-db:refresh-all-database (62ms)
2025-8-2 12:02:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:14:43-debug: refresh db internal success
2025-8-2 12:14:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PointGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:14:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PointBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:14:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:14:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:14:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\EmitterArcGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:14:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:14:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:14:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:14:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:14:43-debug: refresh db assets success
2025-8-2 12:14:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:14:43-debug: asset-db:refresh-all-database (60ms)
2025-8-2 12:15:06-debug: refresh db internal success
2025-8-2 12:15:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:15:06-debug: refresh db assets success
2025-8-2 12:15:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:15:06-debug: asset-db:refresh-all-database (46ms)
2025-8-2 12:15:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:15:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:15:10-debug: refresh db internal success
2025-8-2 12:15:10-debug: refresh db assets success
2025-8-2 12:15:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:15:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:15:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:15:10-debug: asset-db:refresh-all-database (46ms)
2025-8-2 12:15:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:15:12-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:15:12-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (6ms)
2025-8-2 12:15:27-debug: refresh db internal success
2025-8-2 12:15:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:15:27-debug: refresh db assets success
2025-8-2 12:15:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:15:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:15:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:15:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:15:27-debug: asset-db:refresh-all-database (70ms)
2025-8-2 12:15:40-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:15:40-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (3ms)
2025-8-2 12:16:16-debug: refresh db internal success
2025-8-2 12:16:16-debug: refresh db assets success
2025-8-2 12:16:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:16:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:16:16-debug: asset-db:refresh-all-database (61ms)
2025-8-2 12:16:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:16:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:16:35-debug: refresh db internal success
2025-8-2 12:16:35-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:16:35-debug: refresh db assets success
2025-8-2 12:16:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:16:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:16:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:16:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:16:35-debug: asset-db:refresh-all-database (107ms)
2025-8-2 12:16:42-debug: refresh db internal success
2025-8-2 12:16:42-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:16:42-debug: refresh db assets success
2025-8-2 12:16:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:16:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:16:42-debug: asset-db:refresh-all-database (50ms)
2025-8-2 12:16:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:16:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:18:22-debug: refresh db internal success
2025-8-2 12:18:22-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:18:22-debug: refresh db assets success
2025-8-2 12:18:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:18:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:18:22-debug: asset-db:refresh-all-database (62ms)
2025-8-2 12:21:15-debug: refresh db internal success
2025-8-2 12:21:15-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:21:15-debug: refresh db assets success
2025-8-2 12:21:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:21:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:21:15-debug: asset-db:refresh-all-database (61ms)
2025-8-2 12:21:47-debug: refresh db internal success
2025-8-2 12:21:47-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:21:47-debug: refresh db assets success
2025-8-2 12:21:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:21:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:21:47-debug: asset-db:refresh-all-database (60ms)
2025-8-2 12:21:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:21:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:21:48-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:21:48-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (2ms)
2025-8-2 12:22:09-debug: refresh db internal success
2025-8-2 12:22:09-debug: refresh db assets success
2025-8-2 12:22:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:22:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:22:09-debug: asset-db:refresh-all-database (47ms)
2025-8-2 12:22:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:22:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:22:29-debug: refresh db internal success
2025-8-2 12:22:29-debug: refresh db assets success
2025-8-2 12:22:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:22:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:22:29-debug: asset-db:refresh-all-database (47ms)
2025-8-2 12:22:55-debug: refresh db internal success
2025-8-2 12:22:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:22:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:22:55-debug: refresh db assets success
2025-8-2 12:22:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:22:55-debug: asset-db:refresh-all-database (48ms)
2025-8-2 12:22:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:22:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:23:17-debug: refresh db internal success
2025-8-2 12:23:17-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:23:17-debug: refresh db assets success
2025-8-2 12:23:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:23:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:23:17-debug: asset-db:refresh-all-database (59ms)
2025-8-2 12:23:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:23:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:24:05-debug: refresh db internal success
2025-8-2 12:24:05-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:24:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:24:05-debug: refresh db assets success
2025-8-2 12:24:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:24:05-debug: asset-db:refresh-all-database (68ms)
2025-8-2 12:24:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:24:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-2 12:24:14-debug: refresh db internal success
2025-8-2 12:24:14-debug: refresh db assets success
2025-8-2 12:24:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:24:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:24:14-debug: asset-db:refresh-all-database (47ms)
2025-8-2 12:24:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:24:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:25:37-debug: refresh db internal success
2025-8-2 12:25:37-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:25:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:25:37-debug: refresh db assets success
2025-8-2 12:25:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:25:37-debug: asset-db:refresh-all-database (49ms)
2025-8-2 12:25:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:25:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:27:55-debug: refresh db internal success
2025-8-2 12:27:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:27:55-debug: refresh db assets success
2025-8-2 12:27:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:27:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:27:55-debug: asset-db:refresh-all-database (61ms)
2025-8-2 12:27:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:27:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:39:45-debug: refresh db internal success
2025-8-2 12:39:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Data\PathPoint.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:39:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Data
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:39:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:39:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PointGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:39:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:39:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PathBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:39:45-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PointBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:39:45-debug: refresh db assets success
2025-8-2 12:39:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:39:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:39:45-debug: asset-db:refresh-all-database (69ms)
2025-8-2 12:39:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:39:59-debug: refresh db internal success
2025-8-2 12:39:59-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:39:59-debug: refresh db assets success
2025-8-2 12:39:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:39:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:39:59-debug: asset-db:refresh-all-database (63ms)
2025-8-2 12:40:11-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:40:11-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (2ms)
2025-8-2 12:40:11-debug: refresh db internal success
2025-8-2 12:40:11-debug: refresh db assets success
2025-8-2 12:40:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:40:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:40:11-debug: asset-db:refresh-all-database (47ms)
2025-8-2 12:46:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\examples
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:46:43-debug: refresh db internal success
2025-8-2 12:46:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:46:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:46:43-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:46:43-debug: refresh db assets success
2025-8-2 12:46:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:46:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:46:43-debug: asset-db:refresh-all-database (63ms)
2025-8-2 12:47:26-debug: refresh db internal success
2025-8-2 12:47:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:47:26-debug: refresh db assets success
2025-8-2 12:47:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:47:26-debug: asset-db:refresh-all-database (71ms)
2025-8-2 12:47:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:47:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:49:46-debug: refresh db internal success
2025-8-2 12:49:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:49:46-debug: refresh db assets success
2025-8-2 12:49:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:49:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:49:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:49:46-debug: asset-db:refresh-all-database (64ms)
2025-8-2 12:49:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:50:05-debug: refresh db internal success
2025-8-2 12:50:05-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:50:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:50:05-debug: refresh db assets success
2025-8-2 12:50:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:50:05-debug: asset-db:refresh-all-database (48ms)
2025-8-2 12:50:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:50:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:50:08-debug: refresh db internal success
2025-8-2 12:50:08-debug: refresh db assets success
2025-8-2 12:50:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:50:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:50:08-debug: asset-db:refresh-all-database (48ms)
2025-8-2 12:50:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:50:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:51:06-debug: refresh db internal success
2025-8-2 12:51:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PointBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:51:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:51:06-debug: refresh db assets success
2025-8-2 12:51:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:51:06-debug: asset-db:refresh-all-database (60ms)
2025-8-2 12:53:15-debug: refresh db internal success
2025-8-2 12:53:15-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:53:15-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PointBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:53:15-debug: refresh db assets success
2025-8-2 12:53:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:53:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:53:15-debug: asset-db:refresh-all-database (65ms)
2025-8-2 12:53:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:54:17-debug: refresh db internal success
2025-8-2 12:54:17-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:54:17-debug: refresh db assets success
2025-8-2 12:54:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:54:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:54:17-debug: asset-db:refresh-all-database (59ms)
2025-8-2 12:55:13-debug: refresh db internal success
2025-8-2 12:55:13-debug: refresh db assets success
2025-8-2 12:55:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:55:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:55:13-debug: asset-db:refresh-all-database (56ms)
2025-8-2 12:55:38-debug: refresh db internal success
2025-8-2 12:55:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:55:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:55:38-debug: refresh db assets success
2025-8-2 12:55:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:55:38-debug: asset-db:refresh-all-database (61ms)
2025-8-2 12:56:14-debug: refresh db internal success
2025-8-2 12:56:14-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Data\PathPoint.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:56:14-debug: refresh db assets success
2025-8-2 12:56:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:56:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:56:14-debug: asset-db:refresh-all-database (66ms)
2025-8-2 12:56:14-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-2 12:56:14-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-2 12:56:49-debug: refresh db internal success
2025-8-2 12:56:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PointBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:56:49-debug: refresh db assets success
2025-8-2 12:56:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:56:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:56:49-debug: asset-db:refresh-all-database (63ms)
2025-8-2 12:56:49-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-2 12:56:49-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-2 12:57:12-debug: refresh db internal success
2025-8-2 12:57:12-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Data\PathPoint.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:57:12-debug: refresh db assets success
2025-8-2 12:57:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:57:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:57:12-debug: asset-db:refresh-all-database (57ms)
2025-8-2 12:57:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:57:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:57:18-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:57:18-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (2ms)
2025-8-2 12:57:18-debug: refresh db internal success
2025-8-2 12:57:18-debug: refresh db assets success
2025-8-2 12:57:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:57:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:57:18-debug: asset-db:refresh-all-database (47ms)
2025-8-2 12:57:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:57:40-debug: refresh db internal success
2025-8-2 12:57:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\Baker\PointBaker.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:57:40-debug: refresh db assets success
2025-8-2 12:57:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:57:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:57:40-debug: asset-db:refresh-all-database (63ms)
2025-8-2 12:57:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:57:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:58:17-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:58:17-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (3ms)
2025-8-2 12:58:32-debug: refresh db internal success
2025-8-2 12:58:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:58:32-debug: refresh db assets success
2025-8-2 12:58:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:58:32-debug: asset-db:refresh-all-database (47ms)
2025-8-2 12:58:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:58:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:58:34-debug: refresh db internal success
2025-8-2 12:58:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:58:34-debug: refresh db assets success
2025-8-2 12:58:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:58:34-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-2 12:58:34-debug: asset-db:refresh-all-database (52ms)
2025-8-2 12:58:34-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-2 12:58:37-debug: refresh db internal success
2025-8-2 12:58:37-debug: refresh db assets success
2025-8-2 12:58:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:58:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:58:37-debug: asset-db:refresh-all-database (45ms)
2025-8-2 12:58:37-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-2 12:58:37-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-2 12:58:58-debug: refresh db internal success
2025-8-2 12:58:58-debug: refresh db assets success
2025-8-2 12:58:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:58:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:58:58-debug: asset-db:refresh-all-database (54ms)
2025-8-2 12:58:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 12:58:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:59:39-debug: refresh db internal success
2025-8-2 12:59:39-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 12:59:39-debug: refresh db assets success
2025-8-2 12:59:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 12:59:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 12:59:39-debug: asset-db:refresh-all-database (62ms)
2025-8-2 12:59:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 12:59:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 14:02:36-debug: refresh db internal success
2025-8-2 14:02:36-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 14:02:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:02:36-debug: refresh db assets success
2025-8-2 14:02:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:02:36-debug: asset-db:refresh-all-database (62ms)
2025-8-2 14:03:38-debug: refresh db internal success
2025-8-2 14:03:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 14:03:38-debug: refresh db assets success
2025-8-2 14:03:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:03:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:03:38-debug: asset-db:refresh-all-database (58ms)
2025-8-2 14:04:38-debug: refresh db internal success
2025-8-2 14:04:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 14:04:38-debug: refresh db assets success
2025-8-2 14:04:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:04:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:04:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 14:04:38-debug: asset-db:refresh-all-database (77ms)
2025-8-2 14:04:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 14:05:38-debug: refresh db internal success
2025-8-2 14:05:38-debug: refresh db assets success
2025-8-2 14:05:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:05:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:05:38-debug: asset-db:refresh-all-database (52ms)
2025-8-2 14:06:59-debug: refresh db internal success
2025-8-2 14:06:59-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 14:06:59-debug: refresh db assets success
2025-8-2 14:06:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:06:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:06:59-debug: asset-db:refresh-all-database (65ms)
2025-8-2 14:06:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 14:06:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 14:07:32-debug: refresh db internal success
2025-8-2 14:07:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:07:32-debug: refresh db assets success
2025-8-2 14:07:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:07:32-debug: asset-db:refresh-all-database (61ms)
2025-8-2 14:07:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 14:07:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 14:08:24-debug: refresh db internal success
2025-8-2 14:08:24-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 14:08:24-debug: refresh db assets success
2025-8-2 14:08:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:08:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:08:24-debug: asset-db:refresh-all-database (65ms)
2025-8-2 14:08:30-debug: refresh db internal success
2025-8-2 14:08:30-debug: refresh db assets success
2025-8-2 14:08:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:08:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:08:30-debug: asset-db:refresh-all-database (75ms)
2025-8-2 14:09:32-debug: refresh db internal success
2025-8-2 14:09:32-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 14:09:32-debug: refresh db assets success
2025-8-2 14:09:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:09:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:09:32-debug: asset-db:refresh-all-database (55ms)
2025-8-2 14:09:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 14:09:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 14:09:58-debug: refresh db internal success
2025-8-2 14:09:58-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 14:09:58-debug: refresh db assets success
2025-8-2 14:09:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:09:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:09:58-debug: asset-db:refresh-all-database (61ms)
2025-8-2 14:09:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-2 14:09:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-2 14:11:03-debug: refresh db internal success
2025-8-2 14:11:03-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\GizmoDrawer.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 14:11:03-debug: refresh db assets success
2025-8-2 14:11:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:11:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:11:03-debug: asset-db:refresh-all-database (50ms)
2025-8-2 14:11:28-debug: refresh db internal success
2025-8-2 14:11:28-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\gizmos\PathGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-8-2 14:11:28-debug: refresh db assets success
2025-8-2 14:11:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-2 14:11:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-2 14:11:28-debug: asset-db:refresh-all-database (62ms)
